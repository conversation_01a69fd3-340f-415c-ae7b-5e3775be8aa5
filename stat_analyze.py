#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话数据统计分析工具 (Conversation Data Statistical Analysis Tool)

此脚本用于分析OpenAI格式的对话数据，生成统计信息和可视化图表。

用法:
    python stat_analyze.py input_file [--output_dir OUTPUT_DIR]

参数:
    input_file            输入的JSON文件路径，包含OpenAI格式的对话数据
    --output_dir, -o      输出文件的目录路径 (默认: ./output)

数据格式:
    输入文件应为JSON数组，每个元素包含：
    - persona_id: 人格ID（可选）
    - messages: 消息列表，每个消息包含role（"user"或"assistant"）和content

主要功能:
    1. 对话轮次分析 - 统计对话轮数分布并生成直方图
    2. 消息长度分析 - 分析用户和助手消息长度并生成分布图
    3. 回复完整性分析 - 检测助手空回复或过短回复占比并生成饼图
    4. 用户-助手消息长度关系分析 - 分析用户输入与助手回复长度的关系
    5. 回复相似度分析 - 使用余弦相似度和最长公共子串检测相似回复

输出:
    - 各类统计图表 (PNG格式)
    - 相似度分析结果 (JSON格式)

注意:
    - 消息长度统计中，英文按单词计数，中文按字符计数
    - 对话轮次定义为用户和助手一问一答为一轮
"""

import json
import re
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter, defaultdict
import seaborn as sns
from matplotlib.font_manager import FontProperties
import matplotlib as mpl
import platform
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import os
import argparse
import jieba  # 用于中文分词


# 设置中文字体和解决负号显示问题
def setup_chinese_font():
    """设置中文字体"""
    font_path = '/home/<USER>/DataCleaning/Ubuntu_18.04_SimHei.ttf'
    if os.path.exists(font_path):
        try:
            from matplotlib.font_manager import FontProperties, fontManager
            fontManager.addfont(font_path)
            plt.rcParams['font.sans-serif'] = ['SimHei']
            plt.rcParams['font.family'] = 'sans-serif'
            print(f"已加载中文字体: {font_path}")
        except Exception as e:
            print(f"加载中文字体失败: {e}")
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    else:
        print(f"警告: 中文字体文件不存在: {font_path}")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

    # 解决保存图像时负号'-'显示为方块的问题
    plt.rcParams['axes.unicode_minus'] = False

# 初始化字体设置
setup_chinese_font()


# 数据加载与解析相关函数
def load_data(file_path):
    """
    加载JSON数据文件
    
    参数:
        file_path: JSON文件路径
    
    返回:
        解析后的JSON数据
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def extract_conversations(data):
    """
    从OpenAI格式的数据中提取对话信息

    参数:
        data: 解析后的JSON数据，格式为包含persona_id和messages的对话列表

    返回:
        conversations: 有效对话列表
    """
    conversations = []
    skipped_count = 0

    for idx, item in enumerate(data):
        # 检查是否有必要的字段
        if 'messages' not in item:
            skipped_count += 1
            continue

        # 初始化对话信息
        conversation = {
            'id': item.get('persona_id', f'conversation_{idx}'),  # 使用persona_id作为ID，如果没有则生成一个
            'messages': item['messages'],
            'user_messages': [],
            'assistant_messages': []  # 改名为assistant_messages以匹配OpenAI格式
        }

        # 分离用户和助手消息
        for msg in item['messages']:
            if msg.get('role') == 'user':
                content = msg.get('content', '').strip()
                if content:  # 只添加非空内容
                    conversation['user_messages'].append(content)
            elif msg.get('role') == 'assistant':
                content = msg.get('content', '').strip()
                if content:  # 只添加非空内容
                    conversation['assistant_messages'].append(content)

        # 计算对话轮次：用户和助手一问一答为一轮
        user_msg_count = len(conversation['user_messages'])
        assistant_msg_count = len(conversation['assistant_messages'])

        # 最多轮数应该是两者中的较小值
        conversation['turns'] = min(user_msg_count, assistant_msg_count)

        # 处理特殊情况：如果对话以用户消息结尾，且用户消息数比助手消息多一条，则增加一轮
        if user_msg_count > assistant_msg_count:
            conversation['turns'] += 1

        # 只添加有实际对话内容的会话
        if conversation['turns'] > 0:
            conversations.append(conversation)
        else:
            skipped_count += 1

    if skipped_count > 0:
        print(f"跳过了 {skipped_count} 条无效对话（无有效消息或格式不正确）")

    return conversations

# 统计分析函数
def analyze_conversation_turns(conversations):
    """
    分析对话轮次分布
    
    参数:
        conversations: 对话列表
    
    返回:
        turns_data: 对话轮次统计数据
        min_turns: 最小轮次数
        max_turns: 最大轮次数
        max_turns_id: 最大轮次对应的conversation_id
    """
    turns = [conv['turns'] for conv in conversations]
    
    # 找出最大轮次对应的conversation_id
    max_turns = max(turns)
    max_turns_id = None
    min_turns = min(turns)
    min_turns_id = None
    
    for conv in conversations:
        if conv['turns'] == max_turns:
            max_turns_id = conv['id']
            break
    
    return turns, min_turns, max_turns, max_turns_id

def count_message_length(message):
    """
    计算消息的长度，英文单词计为1个单位，中文字符计为1个单位
    
    参数:
        message: 消息文本
    
    返回:
        length: 消息的语义长度
    """
    # 检测英文单词
    english_words = re.findall(r'[a-zA-Z]+', message)
    
    # 计算英文单词的数量
    english_word_count = len(english_words)
    
    # 将英文部分替换为空，以便后续只计算非英文部分
    message_without_english = re.sub(r'[a-zA-Z]+', '', message)
    
    # 计算非英文部分的字符数（包括中文、数字和标点）
    non_english_count = len(message_without_english.strip())
    
    # 总长度为英文单词数加上非英文字符数
    total_length = english_word_count + non_english_count
    
    return total_length

def analyze_message_lengths(conversations):
    """
    分析用户和助手回复长度分布

    参数:
        conversations: 对话列表

    返回:
        user_lengths: 用户消息长度列表
        assistant_lengths: 助手消息长度列表
        user_min_length: 用户最短消息长度
        user_max_length: 用户最长消息长度
        user_max_id: 用户最长消息对应的conversation_id
        assistant_min_length: 助手最短消息长度
        assistant_max_length: 助手最长消息长度
        assistant_max_id: 助手最长消息对应的conversation_id
    """
    user_lengths = []
    assistant_lengths = []
    user_messages_with_id = []  # (length, conv_id, message)
    assistant_messages_with_id = []  # (length, conv_id, message)

    for conv in conversations:
        for msg in conv['user_messages']:
            # 注意：修改为使用新的计数方法，英文单词计为1个单位，中文字符计为1个单位
            length = count_message_length(msg)
            user_lengths.append(length)
            user_messages_with_id.append((length, conv['id'], msg))

        for msg in conv['assistant_messages']:
            # 注意：修改为使用新的计数方法，英文单词计为1个单位，中文字符计为1个单位
            length = count_message_length(msg)
            assistant_lengths.append(length)
            assistant_messages_with_id.append((length, conv['id'], msg))

    # 找出最长和最短消息
    user_min_length = min(user_lengths) if user_lengths else 0
    user_max_length = max(user_lengths) if user_lengths else 0
    assistant_min_length = min(assistant_lengths) if assistant_lengths else 0
    assistant_max_length = max(assistant_lengths) if assistant_lengths else 0

    # 找出最长消息对应的conversation_id
    user_max_id = None
    assistant_max_id = None

    for length, conv_id, _ in user_messages_with_id:
        if length == user_max_length:
            user_max_id = conv_id
            break

    for length, conv_id, _ in assistant_messages_with_id:
        if length == assistant_max_length:
            assistant_max_id = conv_id
            break

    return user_lengths, assistant_lengths, user_min_length, user_max_length, user_max_id, assistant_min_length, assistant_max_length, assistant_max_id

def has_repeated_chars_or_words(message):
    """
    检测消息中是否包含重复的字符或单词
    
    参数:
        message: 消息文本
    
    返回:
        bool: 如果包含重复字符或单词则返回True，否则返回False
    """
    # 分别检测中文重复字符和英文重复单词
    has_repeated = False
    
    # 1. 检测中文重复字符（连续3个或更多相同字符）
    # 中文字符范围大致为：[\u4e00-\u9fff] （简体和繁体中文）
    chinese_text = ''.join(re.findall(r'[\u4e00-\u9fff]+', message))
    if chinese_text:
        chinese_pattern = re.compile(r'([\u4e00-\u9fff])\1{2,}')  # 连续3个或更多相同中文字符
        if chinese_pattern.search(chinese_text):
            return True
    
    # 2. 检测英文重复单词（连续3个或更多相同单词）
    # 提取所有英文单词
    words = re.findall(r'\b[a-zA-Z]+\b', message.lower())  # 转为小写以忽略大小写差异
    
    # 检查连续重复单词
    if len(words) >= 3:
        for i in range(len(words) - 2):
            if words[i] == words[i+1] == words[i+2]:
                return True
    
    # 3. 检测数字或特殊字符的重复（保留原有逻辑，但排除字母）
    # 查找连续3个或更多相同的非字母字符（数字、标点等）
    non_letter_pattern = re.compile(r'([^a-zA-Z\u4e00-\u9fff])\1{2,}')
    if non_letter_pattern.search(message):
        return True
    
    return False

def analyze_repeated_chars(conversations):
    """
    分析重复字符和重复单词的统计

    参数:
        conversations: 对话列表

    返回:
        repeated_count: 包含重复字符或单词的消息数量
        total_messages: 总消息数量
    """
    repeated_count = 0
    total_messages = 0

    for conv in conversations:
        total_messages += len(conv['user_messages']) + len(conv['assistant_messages'])

        for msg in conv['user_messages']:
            if has_repeated_chars_or_words(msg):
                repeated_count += 1

        for msg in conv['assistant_messages']:
            if has_repeated_chars_or_words(msg):
                repeated_count += 1

    return repeated_count, total_messages

def analyze_incomplete_responses(conversations):
    """
    分析不完整回复的占比（助手的空回复或过短回复）

    参数:
        conversations: 对话列表

    返回:
        empty_responses_count: 空回复或过短回复数量
        total_responses: 总回复数量
    """
    empty_responses_count = 0
    total_responses = 0

    for conv in conversations:
        # 遍历助手消息
        for msg in conv['assistant_messages']:
            total_responses += 1
            # 检查是否为空回复或过短回复（少于5个字符）
            if not msg.strip() or len(msg.strip()) < 5:
                empty_responses_count += 1

    return empty_responses_count, total_responses

def analyze_message_length_relationship(conversations):
    """
    分析用户发言长度与助手回复长度的关系

    参数:
        conversations: 对话列表

    返回:
        user_length_assistant_length_pairs: 包含所有用户发言和助手回复长度对的列表 [(用户长度, 助手长度), ...]
        user_length_assistant_length_pairs_no_empty: 排除助手空回复后的长度对列表
    """
    user_length_assistant_length_pairs = []
    user_length_assistant_length_pairs_no_empty = []

    for conv in conversations:
        # 获取用户和助手的消息列表
        user_messages = conv['user_messages']
        assistant_messages = conv['assistant_messages']

        # 计算每一轮问答的长度对
        # 取两个列表中较短的长度，确保一一对应
        for i in range(min(len(user_messages), len(assistant_messages))):
            user_length = count_message_length(user_messages[i])
            assistant_length = count_message_length(assistant_messages[i])

            # 将所有问答对加入第一个列表
            user_length_assistant_length_pairs.append((user_length, assistant_length))

            # 只有当助手回复不为空且长度大于5时，才加入第二个列表
            if assistant_messages[i].strip() != "" and len(assistant_messages[i].strip()) >= 5:
                user_length_assistant_length_pairs_no_empty.append((user_length, assistant_length))

    return user_length_assistant_length_pairs, user_length_assistant_length_pairs_no_empty

def analyze_conversation_level_lengths(conversations):
    """
    分析以会话为单位的用户发言长度与助手回复长度的关系

    参数:
        conversations: 对话列表

    返回:
        conversation_avg_lengths: 每个会话的用户和助手平均长度 [(conversation_id, 用户平均长度, 助手平均长度), ...]
    """
    conversation_avg_lengths = []

    for conv in conversations:
        conv_id = conv['id']
        user_messages = conv['user_messages']
        assistant_messages = conv['assistant_messages']

        # 计算用户消息总长度和数量
        user_total_length = sum(count_message_length(msg) for msg in user_messages)
        user_count = len(user_messages)

        # 筛选出非空的助手消息（长度大于5）
        non_empty_assistant_messages = [msg for msg in assistant_messages if msg.strip() != "" and len(msg.strip()) >= 5]

        # 计算助手非空消息总长度和数量
        if non_empty_assistant_messages:  # 确保有非空回复
            assistant_total_length = sum(count_message_length(msg) for msg in non_empty_assistant_messages)
            assistant_count = len(non_empty_assistant_messages)

            # 计算平均长度
            user_avg_length = user_total_length / user_count if user_count > 0 else 0
            assistant_avg_length = assistant_total_length / assistant_count if assistant_count > 0 else 0

            # 只有当两者都有有效数据时才添加
            if user_avg_length > 0 and assistant_avg_length > 0:
                conversation_avg_lengths.append((conv_id, user_avg_length, assistant_avg_length))

    return conversation_avg_lengths

def analyze_word_frequency(conversations, role='user', top_n=20):
    """
    分析用户或助手消息中的词频

    参数:
        conversations: 对话列表
        role: 分析角色，'user' 或 'assistant'
        top_n: 返回前N个高频词

    返回:
        word_freq: 词频统计结果 [(词, 频次), ...]
    """
    all_text = []

    # 收集所有文本
    for conv in conversations:
        if role == 'user':
            messages = conv['user_messages']
        else:
            messages = conv['assistant_messages']

        for msg in messages:
            all_text.append(msg)

    # 合并所有文本
    combined_text = ' '.join(all_text)

    # 中文分词
    words = jieba.lcut(combined_text)

    # 过滤停用词和标点符号
    stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '这个', '那个', '可以', '但是', '因为', '所以', '如果', '虽然', '然后', '还是', '或者', '而且', '不过', '只是', '已经', '应该', '可能', '比较', '非常', '特别', '真的', '确实', '当然', '其实', '主要', '基本', '一般', '通常', '经常', '总是', '从来', '永远', '马上', '立刻', '现在', '以前', '以后', '今天', '明天', '昨天'}

    # 过滤词汇
    filtered_words = []
    for word in words:
        # 去除标点符号、数字、单字符和停用词
        if (len(word) > 1 and
            word not in stop_words and
            not re.match(r'^[0-9\W]+$', word) and
            not re.match(r'^[a-zA-Z]$', word)):
            filtered_words.append(word)

    # 统计词频
    word_counts = Counter(filtered_words)

    return word_counts.most_common(top_n)

def plot_word_frequency(word_freq_user, word_freq_assistant, output_file="word_frequency.png"):
    """
    绘制用户和助手的词频分析图

    参数:
        word_freq_user: 用户词频统计结果
        word_freq_assistant: 助手词频统计结果
        output_file: 输出文件名
    """
    fig, axes = plt.subplots(1, 2, figsize=(20, 8))

    # 用户词频图
    if word_freq_user:
        words_user, counts_user = zip(*word_freq_user)
        axes[0].barh(range(len(words_user)), counts_user, color='skyblue')
        axes[0].set_yticks(range(len(words_user)))
        axes[0].set_yticklabels(words_user)
        axes[0].set_xlabel('Frequency')
        axes[0].set_title(f'Top {len(words_user)} User Keywords')
        axes[0].invert_yaxis()  # 最高频的词在顶部

        # 在柱子上添加数值
        for i, count in enumerate(counts_user):
            axes[0].text(count + max(counts_user) * 0.01, i, str(count),
                        va='center', ha='left')

    # 助手词频图
    if word_freq_assistant:
        words_assistant, counts_assistant = zip(*word_freq_assistant)
        axes[1].barh(range(len(words_assistant)), counts_assistant, color='lightgreen')
        axes[1].set_yticks(range(len(words_assistant)))
        axes[1].set_yticklabels(words_assistant)
        axes[1].set_xlabel('Frequency')
        axes[1].set_title(f'Top {len(words_assistant)} Assistant Keywords')
        axes[1].invert_yaxis()  # 最高频的词在顶部

        # 在柱子上添加数值
        for i, count in enumerate(counts_assistant):
            axes[1].text(count + max(counts_assistant) * 0.01, i, str(count),
                        va='center', ha='left')

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

def analyze_conversation_depth(conversations):
    """
    分析对话深度（总消息数）分布

    参数:
        conversations: 对话列表

    返回:
        depths: 对话深度列表
        depth_stats: 深度统计信息
    """
    depths = []

    for conv in conversations:
        total_messages = len(conv['user_messages']) + len(conv['assistant_messages'])
        depths.append(total_messages)

    depth_stats = {
        'mean': np.mean(depths),
        'median': np.median(depths),
        'std': np.std(depths),
        'min': min(depths) if depths else 0,
        'max': max(depths) if depths else 0,
        'total_conversations': len(depths)
    }

    return depths, depth_stats

def plot_conversation_depth(depths, depth_stats, output_file="conversation_depth.png"):
    """
    绘制对话深度分布图

    参数:
        depths: 对话深度列表
        depth_stats: 深度统计信息
        output_file: 输出文件名
    """
    fig, axes = plt.subplots(1, 2, figsize=(16, 6))

    # 左图：直方图
    axes[0].hist(depths, bins=30, color='lightcoral', edgecolor='black', alpha=0.7)
    axes[0].axvline(depth_stats['mean'], color='r', linestyle='--',
                   label=f"Mean: {depth_stats['mean']:.1f}")
    axes[0].axvline(depth_stats['median'], color='g', linestyle='-.',
                   label=f"Median: {depth_stats['median']:.1f}")
    axes[0].set_xlabel('Total Messages per Conversation')
    axes[0].set_ylabel('Frequency')
    axes[0].set_title('Distribution of Conversation Depth')
    axes[0].legend()

    # 右图：分组柱状图（根据实际数据分布：大部分是121条消息，平均107条）
    max_depth = depth_stats['max']
    if max_depth <= 50:
        bins = list(range(0, max_depth + 5, 5))
        bin_labels = [f'{i}-{i+4}' for i in range(0, max_depth, 5)]
    elif max_depth <= 150:
        # 针对当前数据集优化：大部分数据在80-121条消息
        bins = [0, 20, 40, 60, 80, 100, 120, max_depth + 1]
        bin_labels = ['0-19', '20-39', '40-59', '60-79', '80-99', '100-119', '120+']
    else:
        bins = [0, 20, 50, 80, 100, 120, 150, 200, max_depth + 1]
        bin_labels = ['0-19', '20-49', '50-79', '80-99', '100-119', '120-149', '150-199', '200+']

    hist, _ = np.histogram(depths, bins=bins)
    axes[1].bar(bin_labels, hist, color='lightcoral', edgecolor='black')
    axes[1].set_xlabel('Message Count Range')
    axes[1].set_ylabel('Number of Conversations')
    axes[1].set_title('Conversation Depth Distribution (Grouped)')

    # 添加数值标签
    for i, count in enumerate(hist):
        if count > 0:
            axes[1].text(i, count + max(hist) * 0.01, str(count),
                        ha='center', va='bottom')

    # 旋转x轴标签
    plt.setp(axes[1].get_xticklabels(), rotation=45, ha='right')

    # 添加统计信息
    stats_text = (f"Total Conversations: {depth_stats['total_conversations']}\n"
                 f"Mean Depth: {depth_stats['mean']:.1f}\n"
                 f"Median Depth: {depth_stats['median']:.1f}\n"
                 f"Std Dev: {depth_stats['std']:.1f}\n"
                 f"Range: {depth_stats['min']}-{depth_stats['max']}")

    axes[1].text(0.95, 0.95, stats_text, transform=axes[1].transAxes,
                verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

# 可视化函数
def plot_conversation_turns(turns_data, output_file="conversation_turns.png"):
    """
    绘制对话轮次分布图
    
    参数:
        turns_data: 对话轮次数据
        output_file: 输出文件名
    """
    # 计算相关统计量
    mean_turns = np.mean(turns_data)
    median_turns = np.median(turns_data)
    max_turns = max(turns_data)
    std_turns = np.std(turns_data)  # 计算标准差
    
    # 创建一个包含两个子图的图表
    fig, axes = plt.subplots(1, 2, figsize=(18, 8))
    
    # 第一个子图：整数直方图，聚焦于主要区间
    axes[0].set_title(f"对话轮次分布 (均值: {mean_turns:.2f}, 标准差: {std_turns:.2f})")
    
    # 设置x轴范围，聚焦于95%的数据
    percentile_95 = np.percentile(turns_data, 95)
    x_max = min(int(percentile_95 * 1.2), max_turns)
    
    # 创建整数bins，每个bin对应一个整数值
    integer_bins = np.arange(0, x_max + 2) - 0.5  # 从-0.5到x_max+0.5，确保每个整数在bin中间
    
    # 使用整数bins绘制直方图
    axes[0].hist(turns_data, bins=integer_bins, color='skyblue', edgecolor='black', alpha=0.7)
    
    # 添加KDE曲线
    if len(turns_data) > 1:  # 确保有足够的数据点计算KDE
        x_kde = np.linspace(0, x_max, 1000)
        kde = sns.kdeplot(turns_data, ax=axes[0], color='navy', linewidth=2)
    
    # 添加均值和中位数线
    axes[0].axvline(mean_turns, color='r', linestyle='--', label=f'均值: {mean_turns:.2f}')
    axes[0].axvline(median_turns, color='g', linestyle='-.', label=f'中位数: {median_turns:.2f}')

    # 添加标准差范围
    axes[0].axvspan(mean_turns - std_turns, mean_turns + std_turns, alpha=0.2, color='red', label=f'±1标准差: {std_turns:.2f}')

    # 设置x轴刻度为整数
    axes[0].set_xticks(np.arange(0, x_max + 1, step=max(1, x_max // 20)))  # 根据x_max自动调整刻度间隔
    axes[0].set_xlim(-0.5, x_max + 0.5)
    axes[0].set_xlabel("对话轮次 (1轮 = 1问1答)")
    axes[0].set_ylabel("频次")
    axes[0].legend()
    
    # 第二个子图：按对话轮次区间分组的柱状图
    # 根据实际数据分布优化分组（大部分数据集中在高轮次）
    if max_turns <= 10:
        bins = list(range(1, max_turns + 2))
        bin_labels = [str(i) for i in range(1, max_turns + 1)]
    elif max_turns <= 30:
        bins = [1, 5, 10, 15, 20, 25, max_turns + 1]
        bin_labels = ['1-4', '5-9', '10-14', '15-19', '20-24', f'25+']
    elif max_turns <= 70:
        # 针对当前数据集优化：大部分数据在40-61轮
        bins = [1, 10, 20, 30, 40, 50, 60, max_turns + 1]
        bin_labels = ['1-9', '10-19', '20-29', '30-39', '40-49', '50-59', f'60+']
    else:
        bins = [1, 10, 20, 30, 40, 50, 60, 80, 100, max_turns + 1]
        bin_labels = ['1-9', '10-19', '20-29', '30-39', '40-49', '50-59', '60-79', '80-99', f'100+']
    
    # 确保bin数组是单调递增的
    for i in range(1, len(bins)):
        if bins[i] <= bins[i-1]:
            bins[i] = bins[i-1] + 1
    
    # 使用numpy的histogram函数计算每个区间的频率
    hist, _ = np.histogram(turns_data, bins=bins)
    
    # 创建柱状图
    axes[1].set_title(f"Distribution of Conversation Turns (StdDev: {std_turns:.2f})")
    bars = axes[1].bar(bin_labels, hist, color='lightblue', edgecolor='black')
    
    # 在每个柱子上方添加具体数值
    for bar in bars:
        height = bar.get_height()
        axes[1].text(bar.get_x() + bar.get_width()/2., height + 5,
                    f'{height}', ha='center', va='bottom')
    
    # 计算每个区间占总数的百分比
    percentages = hist / len(turns_data) * 100
    
    # 在柱子中部添加百分比
    for i, (bar, percentage) in enumerate(zip(bars, percentages)):
        if percentage > 1:  # 只显示大于1%的百分比
            height = bar.get_height() / 2
            axes[1].text(bar.get_x() + bar.get_width()/2., height,
                        f'{percentage:.1f}%', ha='center', va='center',
                        color='black', fontweight='bold')
    
    axes[1].set_xlabel("Number of Turns (1 Turn = 1 Q&A Pair)")
    axes[1].set_ylabel("Number of Conversations")
    
    # 旋转X轴标签，使其更易读
    plt.setp(axes[1].get_xticklabels(), rotation=45, ha='right')
    
    # 添加总体统计信息的文本框
    stats_text = (f"Total Conversations: {len(turns_data)}\n"
                 f"Mean: {mean_turns:.2f} turns\n"
                 f"Std Dev: {std_turns:.2f} turns\n"
                 f"Median: {median_turns:.2f} turns\n"
                 f"Max: {max_turns} turns\n"
                 f"Note: 1 Turn = 1 Q&A Pair")
    axes[1].text(0.95, 0.95, stats_text, transform=axes[1].transAxes,
                verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    plt.tight_layout()
    plt.savefig(output_file)
    plt.close()

def plot_message_lengths(user_lengths, assistant_lengths, output_file="message_lengths.png"):
    """
    绘制用户和助手消息长度分布图

    参数:
        user_lengths: 用户消息长度列表
        assistant_lengths: 助手消息长度列表
        output_file: 输出文件名
    """
    # 创建2行2列的布局，下面一行跨两列
    fig = plt.figure(figsize=(20, 14))
    gs = fig.add_gridspec(2, 2, height_ratios=[1, 1])
    
    # 计算统计量
    user_mean = np.mean(user_lengths)
    user_median = np.median(user_lengths)
    user_std = np.std(user_lengths)
    assistant_mean = np.mean(assistant_lengths)
    assistant_median = np.median(assistant_lengths)
    assistant_std = np.std(assistant_lengths)
    
    # 上方左侧：用户消息长度标准分布，聚焦于95%数据
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.set_title(f"User Message Length (Mean: {user_mean:.2f}, StdDev: {user_std:.2f})", fontsize=14)
    
    # 计算95%分位数，限制x轴范围
    percentile_95 = np.percentile(user_lengths, 95)
    sns.histplot(user_lengths, kde=True, ax=ax1, color='blue')
    ax1.set_xlim(0, percentile_95)  # 限制x轴范围到95%分位数
    ax1.set_xlabel("Length (Words for English, Characters for Chinese)")
    ax1.set_ylabel("Frequency")
    
    # 添加均值和中位数线
    ax1.axvline(user_mean, color='r', linestyle='--', label=f'Mean: {user_mean:.2f}')
    ax1.axvline(user_median, color='g', linestyle='-.', label=f'Median: {user_median:.2f}')
    
    # 添加标准差范围
    ax1.axvspan(user_mean - user_std, user_mean + user_std, alpha=0.2, color='red', label=f'±1 StdDev: {user_std:.2f}')
    
    ax1.legend()
    
    # 上方右侧：用户消息长度分组柱状图
    ax2 = fig.add_subplot(gs[0, 1])
    ax2.set_title(f"User Message Length (StdDev: {user_std:.2f})", fontsize=14)
    
    # 根据实际数据分布优化分组（用户消息平均41字符，95%分位数75字符）
    max_length = max(user_lengths)
    if max_length <= 100:
        bins = [0, 10, 20, 30, 40, 50, 75, max_length + 1]
        bin_labels = ['0-9', '10-19', '20-29', '30-39', '40-49', '50-74', f'75+']
    elif max_length <= 500:
        bins = [0, 10, 20, 30, 50, 75, 100, 150, 200, max_length + 1]
        bin_labels = ['0-9', '10-19', '20-29', '30-49', '50-74', '75-99', '100-149', '150-199', f'200+']
    else:
        bins = [0, 10, 20, 30, 50, 75, 100, 200, 500, 1000, max_length + 1]
        bin_labels = ['0-9', '10-19', '20-29', '30-49', '50-74', '75-99', '100-199', '200-499', '500-999', f'1000+']
    
    # 计算每个区间的频率
    hist, _ = np.histogram(user_lengths, bins=bins)
    
    # 创建柱状图
    bars = ax2.bar(bin_labels, hist, color='lightblue', edgecolor='black')
    
    # 在每个柱子上方添加具体数值
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{height}', ha='center', va='bottom')
    
    # 计算每个区间占总数的百分比
    percentages = hist / len(user_lengths) * 100
    
    # 在柱子中部添加百分比
    for i, (bar, percentage) in enumerate(zip(bars, percentages)):
        if percentage > 1:  # 只显示大于1%的百分比
            height = bar.get_height() / 2
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{percentage:.1f}%', ha='center', va='center',
                    color='black', fontweight='bold')
    
    ax2.set_xlabel("Message Length (Grouped)")
    ax2.set_ylabel("Number of Messages")
    
    # 添加用户消息统计信息
    stats_text_user = (f"User Messages: {len(user_lengths)}\n"
                      f"Mean: {user_mean:.2f}\n"
                      f"Std Dev: {user_std:.2f}\n"
                      f"Median: {user_median:.2f}")
    ax2.text(0.95, 0.95, stats_text_user, transform=ax2.transAxes,
             verticalalignment='top', horizontalalignment='right',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    # 旋转X轴标签，使其更易读
    plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
    
    # 下方：助手消息长度分布（跨两列）
    ax3 = fig.add_subplot(gs[1, :])
    ax3.set_title(f"Assistant Message Length (Mean: {assistant_mean:.2f}, StdDev: {assistant_std:.2f})", fontsize=14)
    sns.histplot(assistant_lengths, kde=True, ax=ax3, color='green')
    ax3.set_xlabel("Length (Words for English, Characters for Chinese)")
    ax3.set_ylabel("Frequency")

    # 添加均值和中位数线
    ax3.axvline(assistant_mean, color='r', linestyle='--', label=f'Mean: {assistant_mean:.2f}')
    ax3.axvline(assistant_median, color='g', linestyle='-.', label=f'Median: {assistant_median:.2f}')

    # 添加标准差范围
    ax3.axvspan(assistant_mean - assistant_std, assistant_mean + assistant_std, alpha=0.2, color='red', label=f'±1 StdDev: {assistant_std:.2f}')

    ax3.legend()

    # 添加助手消息统计信息
    stats_text_assistant = (f"Assistant Messages: {len(assistant_lengths)}\n"
                      f"Mean: {assistant_mean:.2f}\n"
                      f"Std Dev: {assistant_std:.2f}\n"
                      f"Median: {assistant_median:.2f}")
    ax3.text(0.95, 0.95, stats_text_assistant, transform=ax3.transAxes,
             verticalalignment='top', horizontalalignment='right',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    # 添加总体统计信息
    fig.suptitle("Message Length Distributions", fontsize=16)
    
    plt.tight_layout()
    plt.savefig(output_file)
    plt.close()

def plot_combined_pie_charts(repeated_count, total_messages, empty_responses_count, total_responses, output_file="combined_stats.png"):
    """
    将重复字符/单词统计和回复完整性统计合并到一个图表中
    
    参数:
        repeated_count: 包含重复字符或单词的消息数量
        total_messages: 总消息数量
        empty_responses_count: 空回复数量
        total_responses: 总回复数量
        output_file: 输出文件名
    """
    # 创建只包含一个饼图的图表
    plt.figure(figsize=(8, 7))
    
    # 自定义标签格式化函数，同时显示百分比和实际数字
    def autopct_format(values):
        def my_format(pct):
            total = sum(values)
            val = int(round(pct * total / 100.0))
            return '{:.1f}%\n({:d})'.format(pct, val)
        return my_format
    
    # 回复完整性统计饼图
    labels = ['Empty/Short Responses', 'Complete Responses']
    sizes = [empty_responses_count, total_responses - empty_responses_count]
    colors = ['#ff9999', '#66b3ff']

    plt.title("Assistant Response Completeness", fontsize=14)
    plt.pie(sizes, labels=labels, colors=colors, 
           autopct=autopct_format(sizes), startangle=90,
           textprops={'fontsize': 12})
    plt.axis('equal')  # 确保饼图是圆的
    
    # 添加详细统计信息
    stats_text = f"Total Responses: {total_responses}\nEmpty Responses: {empty_responses_count} ({empty_responses_count/total_responses*100:.1f}%)"
    
    plt.text(0, -1.3, stats_text, ha='center', fontsize=12, bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.2)  # 为底部的文本框留出空间
    plt.savefig(output_file)
    plt.close()

def plot_message_length_relationship(user_length_assistant_length_pairs, user_length_assistant_length_pairs_no_empty, output_file="length_relationship.png"):
    """
    绘制用户发言长度与助手回复长度关系的图表

    参数:
        user_length_assistant_length_pairs: 包含所有用户发言和助手回复长度对的列表
        user_length_assistant_length_pairs_no_empty: 排除助手空回复后的长度对列表
        output_file: 输出文件名
    """
    fig, axes = plt.subplots(1, 2, figsize=(15, 7))
    
    # 根据实际数据分布优化用户发言长度分组（平均41字符，95%分位数75字符）
    bins = [0, 10, 20, 30, 40, 50, 75, 100, 200, 500, float('inf')]
    bin_labels = ['0-9', '10-19', '20-29', '30-39', '40-49', '50-74', '75-99', '100-199', '200-499', '500+']
    
    # 处理两个数据集的函数
    def process_data(pairs):
        # 创建字典来存储每个分组的李言回复长度
        group_lengths = {label: [] for label in bin_labels}
        
        # 将每个问答对按用户发言长度分组
        for user_len, liyan_len in pairs:
            for i in range(len(bins) - 1):
                if bins[i] <= user_len < bins[i+1]:
                    group_lengths[bin_labels[i]].append(liyan_len)
                    break
        
        # 计算每个分组的平均回复长度
        x_labels = []
        y_means = []
        counts = []
        
        for label in bin_labels:
            lengths = group_lengths[label]
            if lengths:  # 只有当分组有数据时才添加
                x_labels.append(label)
                y_means.append(np.mean(lengths))
                counts.append(len(lengths))
        
        return x_labels, y_means, counts
    
    # 处理两个数据集
    x_labels1, y_means1, counts1 = process_data(user_length_assistant_length_pairs)
    x_labels2, y_means2, counts2 = process_data(user_length_assistant_length_pairs_no_empty)

    # 绘制第一个图表（包含所有问答对）
    bars1 = axes[0].bar(x_labels1, y_means1, color='skyblue', edgecolor='black')
    axes[0].set_title("User vs. Assistant Message Length\n(All Q&A Pairs)", fontsize=12)
    axes[0].set_xlabel("User Message Length")
    axes[0].set_ylabel("Average Assistant Response Length")
    
    # 在柱子上方添加计数
    for i, (bar, count) in enumerate(zip(bars1, counts1)):
        height = bar.get_height()
        axes[0].text(bar.get_x() + bar.get_width()/2., height + 2,
                    f'n={count}', ha='center', va='bottom', fontsize=8)
    
    # 设置x轴标签旋转
    plt.setp(axes[0].get_xticklabels(), rotation=45, ha='right')
    
    # 绘制第二个图表（排除空回复）
    bars2 = axes[1].bar(x_labels2, y_means2, color='lightgreen', edgecolor='black')
    axes[1].set_title("User vs. Assistant Message Length\n(Excluding Empty/Short Responses)", fontsize=12)
    axes[1].set_xlabel("User Message Length")
    axes[1].set_ylabel("Average Assistant Response Length")
    
    # 在柱子上方添加计数
    for i, (bar, count) in enumerate(zip(bars2, counts2)):
        height = bar.get_height()
        axes[1].text(bar.get_x() + bar.get_width()/2., height + 2,
                    f'n={count}', ha='center', va='bottom', fontsize=8)
    
    # 设置x轴标签旋转
    plt.setp(axes[1].get_xticklabels(), rotation=45, ha='right')
    
    # 添加总体标题
    fig.suptitle("Relationship Between User and Assistant Message Lengths", fontsize=16)
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.85)  # 为总标题留出空间
    plt.savefig(output_file)
    plt.close()

def plot_conversation_level_relationship(conversation_avg_lengths, output_file="conversation_level_relationship.png"):
    """
    绘制以会话为单位的用户发言长度与助手回复长度关系图表

    参数:
        conversation_avg_lengths: 每个会话的用户和助手平均长度列表
        output_file: 输出文件名
    """
    fig, ax = plt.figure(figsize=(12, 8)), plt.gca()
    
    # 根据实际数据分布优化用户发言平均长度分组
    bins = [0, 20, 30, 40, 50, 60, 80, 100, 150, float('inf')]
    bin_labels = ['0-19', '20-29', '30-39', '40-49', '50-59', '60-79', '80-99', '100-149', '150+']
    
    # 创建字典来存储每个分组的李言平均回复长度
    group_avg_lengths = {label: [] for label in bin_labels}
    
    # 将每个会话按用户平均发言长度分组
    for _, user_avg_len, assistant_avg_len in conversation_avg_lengths:
        for i in range(len(bins) - 1):
            if bins[i] <= user_avg_len < bins[i+1]:
                group_avg_lengths[bin_labels[i]].append(assistant_avg_len)
                break
    
    # 计算每个分组的平均回复长度和计数
    x_labels = []
    y_means = []
    counts = []
    
    for label in bin_labels:
        lengths = group_avg_lengths[label]
        if lengths:  # 只有当分组有数据时才添加
            x_labels.append(label)
            y_means.append(np.mean(lengths))
            counts.append(len(lengths))
    
    # 绘制柱状图
    bars = ax.bar(x_labels, y_means, color='coral', edgecolor='black')
    
    # 在柱子上方添加计数
    for i, (bar, count) in enumerate(zip(bars, counts)):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 2,
                f'n={count}', ha='center', va='bottom', fontsize=9)
    
    # 设置标题和标签
    ax.set_title("Conversation-Level Relationship Between User and Assistant Average Message Length\n(Empty/Short Responses Excluded)", fontsize=14)
    ax.set_xlabel("Average User Message Length per Conversation", fontsize=12)
    ax.set_ylabel("Average Assistant Response Length per Conversation", fontsize=12)
    
    # 设置x轴标签旋转
    plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
    
    # 添加解释文本
    explanation = (
        "Note: Each data point represents one conversation.\n"
        "X-axis: Average length of user messages in the conversation.\n"
        "Y-axis: Average length of non-empty assistant responses in the conversation.\n"
        "n=count: Number of conversations in each user length group."
    )
    ax.text(0.5, -0.25, explanation, transform=ax.transAxes, ha='center', va='center', 
            bbox=dict(facecolor='wheat', alpha=0.5), fontsize=10)
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.25)  # 为底部的文本框留出空间
    plt.savefig(output_file)
    plt.close()

def cosine_analyze_response_similarity(conversations, output_file="similarity_results.json"):
    """
    使用TF-IDF和余弦相似度分析助手回复的文本相似度，找出最相似的回复对，并保存到JSON文件

    参数:
        conversations: 对话列表
        output_file: 输出的JSON文件路径

    返回:
        similar_pairs: 相似度高于阈值的回复对列表
    """
    # 收集所有非空的助手回复
    all_responses = []
    response_sources = []  # 记录每个回复的来源（会话ID）

    for conv in conversations:
        conv_id = conv['id']
        for msg in conv['assistant_messages']:
            if msg.strip() and len(msg.strip()) > 10:  # 忽略空回复和过短回复
                all_responses.append(msg)
                response_sources.append(conv_id)
    
    print(f"余弦相似度分析: 共收集 {len(all_responses)} 条有效回复进行相似度分析")
    
    # 如果回复数量太少，无法进行分析
    if len(all_responses) < 2:
        return []
    
    # 使用TF-IDF向量化文本
    vectorizer = TfidfVectorizer(analyzer='char', ngram_range=(2, 3))
    tfidf_matrix = vectorizer.fit_transform(all_responses)
    
    # 计算余弦相似度矩阵
    cosine_sim = cosine_similarity(tfidf_matrix)
    
    # 找出相似度最高的对（不包括自身）
    similar_pairs = []
    n = len(all_responses)
    similarity_threshold = 0.6  # 相似度阈值
    
    for i in range(n):
        for j in range(i+1, n):  # 只比较不同索引，避免自身比较
            # 排除完全相同的文本
            if all_responses[i] == all_responses[j]:
                continue
                
            similarity = cosine_sim[i, j]
            if similarity > similarity_threshold:  # 仅考虑相似度超过阈值的对
                similar_pairs.append((similarity, all_responses[i], all_responses[j], i, j, response_sources[i], response_sources[j]))
    
    # 按相似度降序排序
    similar_pairs.sort(reverse=True, key=lambda x: x[0])
    
    # 将结果转换为JSON友好的格式
    json_results = []
    for similarity, resp1, resp2, idx1, idx2, source1, source2 in similar_pairs:
        json_results.append({
            "similarity": similarity,
            "response1": resp1,
            "response2": resp2,
            "index1": idx1,
            "index2": idx2,
            "source1": source1,
            "source2": source2,
            "is_same_conversation": (source1 == source2)
        })
    
    # 保存到JSON文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_results, f, ensure_ascii=False, indent=4)
    
    print(f"已将 {len(json_results)} 对相似度大于 {similarity_threshold} 的回复保存到 {output_file}")
    
    return similar_pairs

def longest_common_substring(s1, s2):
    """
    计算两个字符串的最长公共子串长度
    
    参数:
        s1: 第一个字符串
        s2: 第二个字符串
        
    返回:
        最长公共子串的长度和内容
    """
    m = len(s1)
    n = len(s2)
    
    # 创建一个(m+1)x(n+1)的二维数组来存储子问题的解
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    max_length = 0  # 记录最长公共子串的长度
    end_pos = 0  # 记录最长公共子串在s1中的结束位置
    
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if s1[i - 1] == s2[j - 1]:
                dp[i][j] = dp[i - 1][j - 1] + 1
                if dp[i][j] > max_length:
                    max_length = dp[i][j]
                    end_pos = i
            else:
                dp[i][j] = 0  # 不相等时重置为0
    
    # 提取最长公共子串
    lcs_content = s1[end_pos - max_length:end_pos] if max_length > 0 else ""
    
    return max_length, lcs_content

def count_meaningful_units(text):
    """
    计算文本中的有意义单位数量（英文按单词计数，中文按字符计数）
    
    参数:
        text: 输入文本
        
    返回:
        有意义单位的数量
    """
    # 提取所有英文单词
    english_words = re.findall(r'\b[a-zA-Z]+\b', text)
    english_count = len(english_words)
    
    # 提取所有中文字符
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
    chinese_count = len(chinese_chars)
    
    # 返回总数
    return english_count + chinese_count

def lcs_analyze_response_similarity(conversations, output_file="lcs_similarity_results.json"):
    """
    使用最长公共子串(LCS)分析助手回复的文本相似度，找出重复文本，并保存到JSON文件
    只比较同一会话内的语句，以降低计算复杂度

    参数:
        conversations: 对话列表
        output_file: 输出的JSON文件路径

    返回:
        lcs_stats: LCS分析的统计信息
    """
    # 最小重复长度阈值
    min_dup_threshold = 8
    
    # 存储所有重复超过阈值的回复对
    duplicate_pairs = []
    lcs_lengths = []
    
    # 按会话分组处理
    total_responses = 0
    
    for conv in conversations:
        conv_id = conv['id']
        
        # 收集当前会话中所有非空的助手回复
        responses = []
        for msg in conv['assistant_messages']:
            if msg.strip() and len(msg.strip()) > 10:  # 忽略空回复和过短回复
                responses.append(msg)
        
        total_responses += len(responses)
        
        # 如果当前会话中的回复数量少于2，则跳过
        if len(responses) < 2:
            continue
        
        # 计算当前会话内所有回复对的LCS
        n = len(responses)
        for i in range(n):
            for j in range(i+1, n):  # 只比较不同索引，避免自身比较
                # 排除完全相同的文本
                if responses[i] == responses[j]:
                    continue
                    
                # 计算最长公共子串长度
                lcs_length, lcs_content = longest_common_substring(responses[i], responses[j])
                
                # 计算LCS中有意义单位的数量（英文单词或中文字符）
                meaningful_units = count_meaningful_units(lcs_content)
                
                lcs_lengths.append(lcs_length)
                
                # 如果重复超过阈值，则记录
                if meaningful_units >= min_dup_threshold:
                    duplicate_pairs.append({
                        "lcs_length": lcs_length,
                        "meaningful_units": meaningful_units,
                        "lcs_content": lcs_content,
                        "response1": responses[i],
                        "response2": responses[j],
                        "source": conv_id
                        # "is_same_conversation": True  # 始终为True，因为只比较同一会话内的语句
                    })
    
    print(f"LCS分析: 共收集 {total_responses} 条有效回复进行分析")
    
    # 如果没有任何重复，返回默认值
    if not lcs_lengths:
        print("未找到任何重复")
        return {"avg_lcs": 0, "max_lcs": 0, "min_lcs": 0, "dup_count": 0}
    
    # 按LCS长度降序排序
    duplicate_pairs.sort(key=lambda x: x["lcs_length"], reverse=True)
    
    # 计算统计信息
    lcs_lengths = np.array(lcs_lengths)
    avg_lcs = np.mean(lcs_lengths) if len(lcs_lengths) > 0 else 0
    max_lcs = np.max(lcs_lengths) if len(lcs_lengths) > 0 else 0
    min_lcs = np.min(lcs_lengths) if len(lcs_lengths) > 0 else 0
    dup_count = sum(1 for l in lcs_lengths if l >= min_dup_threshold)
    
    # 保存到JSON文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(duplicate_pairs, f, ensure_ascii=False, indent=4)
    
    print(f"已将 {len(duplicate_pairs)} 对重复超过 {min_dup_threshold} 个单位的回复保存到 {output_file}")
    print(f"LCS统计: 平均重复长度 = {avg_lcs:.2f}, 最大重复长度 = {max_lcs}, 最小重复长度 = {min_lcs}, 重复轮数 = {dup_count}")
    
    # 返回统计信息
    return {
        "avg_lcs": avg_lcs,
        "max_lcs": max_lcs,
        "min_lcs": min_lcs,
        "dup_count": dup_count
    }

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="对话数据统计分析工具")
    parser.add_argument("input_file", help="输入的JSON文件路径")
    parser.add_argument("--output_dir", "-o", default="./output", help="输出文件的目录路径 (默认: ./output)")
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载数据
    data = load_data(args.input_file)
    
    # 提取对话信息
    conversations = extract_conversations(data)
    
    # 创建输出文件路径
    output_base = os.path.basename(args.input_file).split('.')[0]
    
    # 1. 分析对话轮次分布
    turns_data, min_turns, max_turns, max_turns_id = analyze_conversation_turns(conversations)
    turns_std = np.std(turns_data)  # 计算标准差
    turns_output_file = os.path.join(args.output_dir, f"{output_base}_conversation_turns.png")
    plot_conversation_turns(turns_data, output_file=turns_output_file)
    print(f"对话轮次统计 (一问一答计为一轮): 均值={np.mean(turns_data):.2f}, 标准差={turns_std:.2f}, 中位数={np.median(turns_data):.2f}, "
          f"最小值={min_turns}, 最大值={max_turns}, 最大轮次对话ID={max_turns_id}")
    
    # 2. 分析用户和助手消息长度
    user_lengths, assistant_lengths, user_min_len, user_max_len, user_max_id, assistant_min_len, assistant_max_len, assistant_max_id = analyze_message_lengths(conversations)
    user_std = np.std(user_lengths)  # 计算用户消息长度标准差
    assistant_std = np.std(assistant_lengths)  # 计算助手消息长度标准差
    lengths_output_file = os.path.join(args.output_dir, f"{output_base}_message_lengths.png")
    plot_message_lengths(user_lengths, assistant_lengths, output_file=lengths_output_file)
    print(f"用户消息长度: 均值={np.mean(user_lengths):.2f}, 标准差={user_std:.2f}, 中位数={np.median(user_lengths):.2f}, "
          f"最小值={user_min_len}, 最大值={user_max_len}, 最长消息ID={user_max_id}")
    print(f"助手消息长度: 均值={np.mean(assistant_lengths):.2f}, 标准差={assistant_std:.2f}, 中位数={np.median(assistant_lengths):.2f}, "
          f"最小值={assistant_min_len}, 最大值={assistant_max_len}, 最长消息ID={assistant_max_id}")
    print("注：消息长度统计中，英文按单词计数，中文按字符计数")
    
    # 3. 分析回复完整性
    empty_responses_count, total_responses = analyze_incomplete_responses(conversations)
    
    # 4. 绘制回复完整性饼图
    pie_output_file = os.path.join(args.output_dir, f"{output_base}_response_completeness.png")
    # 传递重复统计数据，但实际上不会使用
    plot_combined_pie_charts(0, 0, empty_responses_count, total_responses, output_file=pie_output_file)
    
    # 5. 分析用户发言长度与助手回复长度的关系
    length_pairs, length_pairs_no_empty = analyze_message_length_relationship(conversations)
    length_rel_output_file = os.path.join(args.output_dir, f"{output_base}_length_relationship.png")
    plot_message_length_relationship(length_pairs, length_pairs_no_empty, output_file=length_rel_output_file)
    print(f"用户-助手消息长度关系: 共分析 {len(length_pairs)} 对问答, 其中非空回复 {len(length_pairs_no_empty)} 对")

    # 6. 以会话为单位分析用户与助手平均发言长度关系
    conversation_avg_lengths = analyze_conversation_level_lengths(conversations)
    conv_rel_output_file = os.path.join(args.output_dir, f"{output_base}_conversation_level_relationship.png")
    plot_conversation_level_relationship(conversation_avg_lengths, output_file=conv_rel_output_file)
    print(f"会话级用户-助手平均长度关系: 共分析 {len(conversation_avg_lengths)} 个有效会话")

    # 7. 回复完整性统计
    print(f"回复完整性: {empty_responses_count}/{total_responses} 回复为空或过短 ({empty_responses_count/total_responses*100:.2f}%)")

    # 8. 使用余弦相似度分析助手回复的文本相似度，并将结果保存到JSON文件
    cosine_output_file = os.path.join(args.output_dir, f"{output_base}_cosine_similarity_results.json")
    similar_pairs = cosine_analyze_response_similarity(conversations, cosine_output_file)

    # 仅输出结果数量
    if similar_pairs:
        print(f"\n余弦相似度: 共找到 {len(similar_pairs)} 对相似度大于0.6的回复")
    else:
        print("\n未找到相似度高的助手回复对")

    # # 9. 使用最长公共子串(LCS)分析助手回复的文本相似度
    # lcs_output_file = os.path.join(args.output_dir, f"{output_base}_lcs_similarity_results.json")
    # lcs_stats = lcs_analyze_response_similarity(conversations, lcs_output_file)

    # 10. 词频分析
    print("\n正在进行词频分析...")
    try:
        word_freq_user = analyze_word_frequency(conversations, role='user', top_n=20)
        word_freq_assistant = analyze_word_frequency(conversations, role='assistant', top_n=20)

        word_freq_output_file = os.path.join(args.output_dir, f"{output_base}_word_frequency.png")
        plot_word_frequency(word_freq_user, word_freq_assistant, output_file=word_freq_output_file)

        print(f"用户高频词汇: {[word for word, _ in word_freq_user[:10]]}")
        print(f"助手高频词汇: {[word for word, _ in word_freq_assistant[:10]]}")
    except ImportError:
        print("警告: 未安装jieba库，跳过词频分析。可使用 'pip install jieba' 安装。")
    except Exception as e:
        print(f"词频分析出错: {e}")

    # 11. 对话深度分析
    print("\n正在进行对话深度分析...")
    depths, depth_stats = analyze_conversation_depth(conversations)
    depth_output_file = os.path.join(args.output_dir, f"{output_base}_conversation_depth.png")
    plot_conversation_depth(depths, depth_stats, output_file=depth_output_file)

    print(f"对话深度统计: 平均消息数={depth_stats['mean']:.1f}, "
          f"中位数={depth_stats['median']:.1f}, "
          f"标准差={depth_stats['std']:.1f}, "
          f"范围={depth_stats['min']}-{depth_stats['max']}")

    print(f"\n分析完成！所有结果已保存到 {args.output_dir} 目录")
    

if __name__ == "__main__":
    main()
