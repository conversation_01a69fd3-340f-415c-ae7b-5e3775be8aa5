#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预览工具 - 用于查看数据分布以便调整可视化参数
"""

import json
import numpy as np
from collections import Counter

def load_data(file_path):
    """加载JSON数据文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def extract_conversations(data):
    """从OpenAI格式的数据中提取对话信息"""
    conversations = []
    skipped_count = 0
    
    for idx, item in enumerate(data):
        if 'messages' not in item:
            skipped_count += 1
            continue
        
        conversation = {
            'id': item.get('persona_id', f'conversation_{idx}'),
            'messages': item['messages'],
            'user_messages': [],
            'assistant_messages': []
        }
        
        for msg in item['messages']:
            if msg.get('role') == 'user':
                content = msg.get('content', '').strip()
                if content:
                    conversation['user_messages'].append(content)
            elif msg.get('role') == 'assistant':
                content = msg.get('content', '').strip()
                if content:
                    conversation['assistant_messages'].append(content)
        
        user_msg_count = len(conversation['user_messages'])
        assistant_msg_count = len(conversation['assistant_messages'])
        conversation['turns'] = min(user_msg_count, assistant_msg_count)
        
        if user_msg_count > assistant_msg_count:
            conversation['turns'] += 1
        
        if conversation['turns'] > 0:
            conversations.append(conversation)
        else:
            skipped_count += 1
    
    if skipped_count > 0:
        print(f"跳过了 {skipped_count} 条无效对话")
    
    return conversations

def count_message_length(message):
    """计算消息长度"""
    import re
    english_words = re.findall(r'[a-zA-Z]+', message)
    english_word_count = len(english_words)
    message_without_english = re.sub(r'[a-zA-Z]+', '', message)
    non_english_count = len(message_without_english.strip())
    return english_word_count + non_english_count

def preview_data_distribution(file_path):
    """预览数据分布"""
    print(f"正在分析文件: {file_path}")
    
    # 加载数据
    data = load_data(file_path)
    conversations = extract_conversations(data)
    
    print(f"\n=== 基本统计 ===")
    print(f"总对话数: {len(conversations)}")
    
    # 对话轮次分析
    turns = [conv['turns'] for conv in conversations]
    print(f"\n=== 对话轮次分布 ===")
    print(f"轮次范围: {min(turns)} - {max(turns)}")
    print(f"平均轮次: {np.mean(turns):.2f}")
    print(f"中位数轮次: {np.median(turns):.2f}")
    print(f"标准差: {np.std(turns):.2f}")
    
    # 轮次分布统计
    turn_counts = Counter(turns)
    print(f"轮次分布 (前10个):")
    for turn, count in turn_counts.most_common(10):
        print(f"  {turn}轮: {count}次 ({count/len(turns)*100:.1f}%)")
    
    # 消息长度分析
    user_lengths = []
    assistant_lengths = []
    
    for conv in conversations:
        for msg in conv['user_messages']:
            user_lengths.append(count_message_length(msg))
        for msg in conv['assistant_messages']:
            assistant_lengths.append(count_message_length(msg))
    
    print(f"\n=== 用户消息长度分布 ===")
    print(f"消息数量: {len(user_lengths)}")
    print(f"长度范围: {min(user_lengths)} - {max(user_lengths)}")
    print(f"平均长度: {np.mean(user_lengths):.2f}")
    print(f"中位数长度: {np.median(user_lengths):.2f}")
    print(f"标准差: {np.std(user_lengths):.2f}")
    print(f"95%分位数: {np.percentile(user_lengths, 95):.2f}")
    print(f"99%分位数: {np.percentile(user_lengths, 99):.2f}")
    
    print(f"\n=== 助手消息长度分布 ===")
    print(f"消息数量: {len(assistant_lengths)}")
    print(f"长度范围: {min(assistant_lengths)} - {max(assistant_lengths)}")
    print(f"平均长度: {np.mean(assistant_lengths):.2f}")
    print(f"中位数长度: {np.median(assistant_lengths):.2f}")
    print(f"标准差: {np.std(assistant_lengths):.2f}")
    print(f"95%分位数: {np.percentile(assistant_lengths, 95):.2f}")
    print(f"99%分位数: {np.percentile(assistant_lengths, 99):.2f}")
    
    # 对话深度分析
    depths = []
    for conv in conversations:
        total_messages = len(conv['user_messages']) + len(conv['assistant_messages'])
        depths.append(total_messages)
    
    print(f"\n=== 对话深度分布 ===")
    print(f"深度范围: {min(depths)} - {max(depths)}")
    print(f"平均深度: {np.mean(depths):.2f}")
    print(f"中位数深度: {np.median(depths):.2f}")
    print(f"标准差: {np.std(depths):.2f}")
    print(f"95%分位数: {np.percentile(depths, 95):.2f}")
    
    # 深度分布统计
    depth_counts = Counter(depths)
    print(f"深度分布 (前10个):")
    for depth, count in depth_counts.most_common(10):
        print(f"  {depth}条消息: {count}次 ({count/len(depths)*100:.1f}%)")
    
    # 空回复分析
    empty_count = 0
    total_assistant_messages = len(assistant_lengths)
    
    for conv in conversations:
        for msg in conv['assistant_messages']:
            if not msg.strip() or len(msg.strip()) < 5:
                empty_count += 1
    
    print(f"\n=== 回复完整性 ===")
    print(f"助手总回复数: {total_assistant_messages}")
    print(f"空/短回复数: {empty_count}")
    print(f"空/短回复比例: {empty_count/total_assistant_messages*100:.2f}%")
    
    return {
        'conversations': len(conversations),
        'turns_range': (min(turns), max(turns)),
        'turns_stats': (np.mean(turns), np.median(turns), np.std(turns)),
        'user_length_range': (min(user_lengths), max(user_lengths)),
        'user_length_stats': (np.mean(user_lengths), np.median(user_lengths), np.std(user_lengths)),
        'assistant_length_range': (min(assistant_lengths), max(assistant_lengths)),
        'assistant_length_stats': (np.mean(assistant_lengths), np.median(assistant_lengths), np.std(assistant_lengths)),
        'depth_range': (min(depths), max(depths)),
        'depth_stats': (np.mean(depths), np.median(depths), np.std(depths)),
        'empty_ratio': empty_count/total_assistant_messages
    }

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 2:
        print("用法: python data_preview.py <json_file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    stats = preview_data_distribution(file_path)
