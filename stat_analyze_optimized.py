#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版对话数据统计分析工具 (Optimized Conversation Data Statistical Analysis Tool)

此脚本用于分析OpenAI格式的对话数据，生成统计信息和可视化图表。
针对实际数据分布进行了优化，支持中文字体显示。

用法:
    python stat_analyze_optimized.py input_file [--output_dir OUTPUT_DIR]

参数:
    input_file            输入的JSON文件路径，包含OpenAI格式的对话数据
    --output_dir, -o      输出文件的目录路径 (默认: ./output)

数据格式:
    输入文件应为JSON数组，每个元素包含：
    - persona_id: 人格ID（可选）
    - messages: 消息列表，每个消息包含role（"user"或"assistant"）和content

主要功能:
    1. 对话轮次分析 - 统计对话轮数分布并生成直方图
    2. 消息长度分析 - 分析用户和助手消息长度并生成分布图
    3. 对话深度分析 - 分析每个对话的总消息数分布
    4. 词频分析 - 分析用户和助手的高频词汇
    5. 回复完整性分析 - 检测助手空回复或过短回复占比

输出:
    - 各类统计图表 (PNG格式)
    - 词频分析结果

注意:
    - 消息长度统计中，英文按单词计数，中文按字符计数
    - 对话轮次定义为用户和助手一问一答为一轮
    - 图表标题和标签使用中文显示
"""

import json
import re
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter, defaultdict
import seaborn as sns
import os
import argparse

# 设置中文字体和解决负号显示问题
def setup_chinese_font():
    """设置中文字体"""
    font_path = '/home/<USER>/DataCleaning/Ubuntu_18.04_SimHei.ttf'
    if os.path.exists(font_path):
        try:
            from matplotlib.font_manager import FontProperties, fontManager
            fontManager.addfont(font_path)
            plt.rcParams['font.sans-serif'] = ['SimHei']
            plt.rcParams['font.family'] = 'sans-serif'
            print(f"已加载中文字体: {font_path}")
        except Exception as e:
            print(f"加载中文字体失败: {e}")
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    else:
        print(f"警告: 中文字体文件不存在: {font_path}")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    
    plt.rcParams['axes.unicode_minus'] = False

setup_chinese_font()

def load_data(file_path):
    """加载JSON数据文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def extract_conversations(data):
    """从OpenAI格式的数据中提取对话信息"""
    conversations = []
    skipped_count = 0
    
    for idx, item in enumerate(data):
        if 'messages' not in item:
            skipped_count += 1
            continue
        
        conversation = {
            'id': item.get('persona_id', f'conversation_{idx}'),
            'messages': item['messages'],
            'user_messages': [],
            'assistant_messages': []
        }
        
        for msg in item['messages']:
            if msg.get('role') == 'user':
                content = msg.get('content', '').strip()
                if content:
                    conversation['user_messages'].append(content)
            elif msg.get('role') == 'assistant':
                content = msg.get('content', '').strip()
                if content:
                    conversation['assistant_messages'].append(content)
        
        user_msg_count = len(conversation['user_messages'])
        assistant_msg_count = len(conversation['assistant_messages'])
        conversation['turns'] = min(user_msg_count, assistant_msg_count)
        
        if user_msg_count > assistant_msg_count:
            conversation['turns'] += 1
        
        if conversation['turns'] > 0:
            conversations.append(conversation)
        else:
            skipped_count += 1
    
    if skipped_count > 0:
        print(f"跳过了 {skipped_count} 条无效对话")
    
    return conversations

def count_message_length(message):
    """计算消息长度"""
    english_words = re.findall(r'[a-zA-Z]+', message)
    english_word_count = len(english_words)
    message_without_english = re.sub(r'[a-zA-Z]+', '', message)
    non_english_count = len(message_without_english.strip())
    return english_word_count + non_english_count

def analyze_conversation_turns(conversations):
    """分析对话轮次分布"""
    turns = [conv['turns'] for conv in conversations]
    max_turns = max(turns)
    max_turns_id = None
    min_turns = min(turns)
    
    for conv in conversations:
        if conv['turns'] == max_turns:
            max_turns_id = conv['id']
            break
    
    return turns, min_turns, max_turns, max_turns_id

def analyze_message_lengths(conversations):
    """分析用户和助手回复长度分布"""
    user_lengths = []
    assistant_lengths = []
    user_messages_with_id = []
    assistant_messages_with_id = []
    
    for conv in conversations:
        for msg in conv['user_messages']:
            length = count_message_length(msg)
            user_lengths.append(length)
            user_messages_with_id.append((length, conv['id'], msg))
        
        for msg in conv['assistant_messages']:
            length = count_message_length(msg)
            assistant_lengths.append(length)
            assistant_messages_with_id.append((length, conv['id'], msg))
    
    user_min_length = min(user_lengths) if user_lengths else 0
    user_max_length = max(user_lengths) if user_lengths else 0
    assistant_min_length = min(assistant_lengths) if assistant_lengths else 0
    assistant_max_length = max(assistant_lengths) if assistant_lengths else 0
    
    user_max_id = None
    assistant_max_id = None
    
    for length, conv_id, _ in user_messages_with_id:
        if length == user_max_length:
            user_max_id = conv_id
            break
    
    for length, conv_id, _ in assistant_messages_with_id:
        if length == assistant_max_length:
            assistant_max_id = conv_id
            break
    
    return user_lengths, assistant_lengths, user_min_length, user_max_length, user_max_id, assistant_min_length, assistant_max_length, assistant_max_id

def analyze_conversation_depth(conversations):
    """分析对话深度（总消息数）分布"""
    depths = []
    
    for conv in conversations:
        total_messages = len(conv['user_messages']) + len(conv['assistant_messages'])
        depths.append(total_messages)
    
    depth_stats = {
        'mean': np.mean(depths),
        'median': np.median(depths),
        'std': np.std(depths),
        'min': min(depths) if depths else 0,
        'max': max(depths) if depths else 0,
        'total_conversations': len(depths)
    }
    
    return depths, depth_stats

def analyze_incomplete_responses(conversations):
    """分析不完整回复的占比（助手的空回复或过短回复）"""
    empty_responses_count = 0
    total_responses = 0
    
    for conv in conversations:
        for msg in conv['assistant_messages']:
            total_responses += 1
            if not msg.strip() or len(msg.strip()) < 5:
                empty_responses_count += 1
    
    return empty_responses_count, total_responses

def analyze_word_frequency(conversations, role='user', top_n=20):
    """分析用户或助手消息中的词频"""
    try:
        import jieba
    except ImportError:
        print("警告: 未安装jieba库，跳过词频分析。可使用 'pip install jieba' 安装。")
        return []
    
    all_text = []
    
    for conv in conversations:
        if role == 'user':
            messages = conv['user_messages']
        else:
            messages = conv['assistant_messages']
        
        for msg in messages:
            all_text.append(msg)
    
    combined_text = ' '.join(all_text)
    words = jieba.lcut(combined_text)
    
    # 过滤停用词和标点符号
    stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '这个', '那个', '可以', '但是', '因为', '所以', '如果', '虽然', '然后', '还是', '或者', '而且', '不过', '只是', '已经', '应该', '可能', '比较', '非常', '特别', '真的', '确实', '当然', '其实', '主要', '基本', '一般', '通常', '经常', '总是', '从来', '永远', '马上', '立刻', '现在', '以前', '以后', '今天', '明天', '昨天'}
    
    filtered_words = []
    for word in words:
        if (len(word) > 1 and 
            word not in stop_words and 
            not re.match(r'^[0-9\W]+$', word) and
            not re.match(r'^[a-zA-Z]$', word)):
            filtered_words.append(word)
    
    word_counts = Counter(filtered_words)
    return word_counts.most_common(top_n)

def plot_conversation_turns(turns_data, output_file="conversation_turns.png"):
    """绘制对话轮次分布图"""
    mean_turns = np.mean(turns_data)
    median_turns = np.median(turns_data)
    max_turns = max(turns_data)
    std_turns = np.std(turns_data)

    fig, axes = plt.subplots(1, 2, figsize=(18, 8))

    # 第一个子图：直方图
    axes[0].set_title(f"对话轮次分布 (均值: {mean_turns:.2f}, 标准差: {std_turns:.2f})")

    percentile_95 = np.percentile(turns_data, 95)
    x_max = min(int(percentile_95 * 1.2), max_turns)

    integer_bins = np.arange(0, x_max + 2) - 0.5
    axes[0].hist(turns_data, bins=integer_bins, color='skyblue', edgecolor='black', alpha=0.7)

    if len(turns_data) > 1:
        sns.kdeplot(turns_data, ax=axes[0], color='navy', linewidth=2)

    axes[0].axvline(mean_turns, color='r', linestyle='--', label=f'均值: {mean_turns:.2f}')
    axes[0].axvline(median_turns, color='g', linestyle='-.', label=f'中位数: {median_turns:.2f}')
    axes[0].axvspan(mean_turns - std_turns, mean_turns + std_turns, alpha=0.2, color='red', label=f'±1标准差: {std_turns:.2f}')

    axes[0].set_xticks(np.arange(0, x_max + 1, step=max(1, x_max // 20)))
    axes[0].set_xlim(-0.5, x_max + 0.5)
    axes[0].set_xlabel("对话轮次 (1轮 = 1问1答)")
    axes[0].set_ylabel("频次")
    axes[0].legend()

    # 第二个子图：分组柱状图（针对数据分布优化）
    if max_turns <= 70:
        bins = [1, 10, 20, 30, 40, 50, 60, max_turns + 1]
        bin_labels = ['1-9', '10-19', '20-29', '30-39', '40-49', '50-59', '60+']
    else:
        bins = [1, 10, 20, 30, 40, 50, 60, 80, 100, max_turns + 1]
        bin_labels = ['1-9', '10-19', '20-29', '30-39', '40-49', '50-59', '60-79', '80-99', '100+']

    hist, _ = np.histogram(turns_data, bins=bins)
    axes[1].set_title(f"对话轮次分布 (标准差: {std_turns:.2f})")
    bars = axes[1].bar(bin_labels, hist, color='lightblue', edgecolor='black')

    for bar in bars:
        height = bar.get_height()
        axes[1].text(bar.get_x() + bar.get_width()/2., height + 5,
                    f'{height}', ha='center', va='bottom')

    percentages = hist / len(turns_data) * 100
    for i, (bar, percentage) in enumerate(zip(bars, percentages)):
        if percentage > 1:
            height = bar.get_height() / 2
            axes[1].text(bar.get_x() + bar.get_width()/2., height,
                        f'{percentage:.1f}%', ha='center', va='center',
                        color='black', fontweight='bold')

    axes[1].set_xlabel("对话轮次 (1轮 = 1问1答)")
    axes[1].set_ylabel("对话数量")
    plt.setp(axes[1].get_xticklabels(), rotation=45, ha='right')

    stats_text = (f"总对话数: {len(turns_data)}\n"
                 f"均值: {mean_turns:.2f} 轮\n"
                 f"标准差: {std_turns:.2f} 轮\n"
                 f"中位数: {median_turns:.2f} 轮\n"
                 f"最大值: {max_turns} 轮")
    axes[1].text(0.95, 0.95, stats_text, transform=axes[1].transAxes,
                verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

def plot_message_lengths(user_lengths, assistant_lengths, output_file="message_lengths.png"):
    """绘制用户和助手消息长度分布图"""
    fig = plt.figure(figsize=(20, 14))
    gs = fig.add_gridspec(2, 2, height_ratios=[1, 1])

    user_mean = np.mean(user_lengths)
    user_median = np.median(user_lengths)
    user_std = np.std(user_lengths)
    assistant_mean = np.mean(assistant_lengths)
    assistant_median = np.median(assistant_lengths)
    assistant_std = np.std(assistant_lengths)

    # 上方左侧：用户消息长度分布
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.set_title(f"用户消息长度分布 (均值: {user_mean:.2f}, 标准差: {user_std:.2f})", fontsize=14)

    percentile_95 = np.percentile(user_lengths, 95)
    sns.histplot(user_lengths, kde=True, ax=ax1, color='blue')
    ax1.set_xlim(0, percentile_95)
    ax1.set_xlabel("长度 (英文按单词计数，中文按字符计数)")
    ax1.set_ylabel("频次")

    ax1.axvline(user_mean, color='r', linestyle='--', label=f'均值: {user_mean:.2f}')
    ax1.axvline(user_median, color='g', linestyle='-.', label=f'中位数: {user_median:.2f}')
    ax1.axvspan(user_mean - user_std, user_mean + user_std, alpha=0.2, color='red', label=f'±1标准差: {user_std:.2f}')
    ax1.legend()

    # 上方右侧：用户消息长度分组柱状图
    ax2 = fig.add_subplot(gs[0, 1])
    ax2.set_title(f"用户消息长度分布 (标准差: {user_std:.2f})", fontsize=14)

    max_length = max(user_lengths)
    if max_length <= 100:
        bins = [0, 10, 20, 30, 40, 50, 75, max_length + 1]
        bin_labels = ['0-9', '10-19', '20-29', '30-39', '40-49', '50-74', '75+']
    elif max_length <= 500:
        bins = [0, 10, 20, 30, 50, 75, 100, 150, 200, max_length + 1]
        bin_labels = ['0-9', '10-19', '20-29', '30-49', '50-74', '75-99', '100-149', '150-199', '200+']
    else:
        bins = [0, 10, 20, 30, 50, 75, 100, 200, 500, 1000, max_length + 1]
        bin_labels = ['0-9', '10-19', '20-29', '30-49', '50-74', '75-99', '100-199', '200-499', '500-999', '1000+']

    hist, _ = np.histogram(user_lengths, bins=bins)
    bars = ax2.bar(bin_labels, hist, color='lightblue', edgecolor='black')

    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{height}', ha='center', va='bottom')

    percentages = hist / len(user_lengths) * 100
    for i, (bar, percentage) in enumerate(zip(bars, percentages)):
        if percentage > 1:
            height = bar.get_height() / 2
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{percentage:.1f}%', ha='center', va='center',
                    color='black', fontweight='bold')

    ax2.set_xlabel("消息长度 (分组)")
    ax2.set_ylabel("消息数量")

    stats_text_user = (f"用户消息数: {len(user_lengths)}\n"
                      f"均值: {user_mean:.2f}\n"
                      f"标准差: {user_std:.2f}\n"
                      f"中位数: {user_median:.2f}")
    ax2.text(0.95, 0.95, stats_text_user, transform=ax2.transAxes,
             verticalalignment='top', horizontalalignment='right',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

    plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')

    # 下方：助手消息长度分布（跨两列）
    ax3 = fig.add_subplot(gs[1, :])
    ax3.set_title(f"助手消息长度分布 (均值: {assistant_mean:.2f}, 标准差: {assistant_std:.2f})", fontsize=14)
    sns.histplot(assistant_lengths, kde=True, ax=ax3, color='green')
    ax3.set_xlabel("长度 (英文按单词计数，中文按字符计数)")
    ax3.set_ylabel("频次")

    ax3.axvline(assistant_mean, color='r', linestyle='--', label=f'均值: {assistant_mean:.2f}')
    ax3.axvline(assistant_median, color='g', linestyle='-.', label=f'中位数: {assistant_median:.2f}')
    ax3.axvspan(assistant_mean - assistant_std, assistant_mean + assistant_std, alpha=0.2, color='red', label=f'±1标准差: {assistant_std:.2f}')
    ax3.legend()

    stats_text_assistant = (f"助手消息数: {len(assistant_lengths)}\n"
                      f"均值: {assistant_mean:.2f}\n"
                      f"标准差: {assistant_std:.2f}\n"
                      f"中位数: {assistant_median:.2f}")
    ax3.text(0.95, 0.95, stats_text_assistant, transform=ax3.transAxes,
             verticalalignment='top', horizontalalignment='right',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

    fig.suptitle("消息长度分布", fontsize=16)

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

def plot_conversation_depth(depths, depth_stats, output_file="conversation_depth.png"):
    """绘制对话深度分布图"""
    fig, axes = plt.subplots(1, 2, figsize=(16, 6))

    # 左图：直方图
    axes[0].hist(depths, bins=30, color='lightcoral', edgecolor='black', alpha=0.7)
    axes[0].axvline(depth_stats['mean'], color='r', linestyle='--',
                   label=f"均值: {depth_stats['mean']:.1f}")
    axes[0].axvline(depth_stats['median'], color='g', linestyle='-.',
                   label=f"中位数: {depth_stats['median']:.1f}")
    axes[0].set_xlabel('每个对话的总消息数')
    axes[0].set_ylabel('频次')
    axes[0].set_title('对话深度分布')
    axes[0].legend()

    # 右图：分组柱状图（针对当前数据集优化）
    max_depth = depth_stats['max']
    if max_depth <= 150:
        bins = [0, 20, 40, 60, 80, 100, 120, max_depth + 1]
        bin_labels = ['0-19', '20-39', '40-59', '60-79', '80-99', '100-119', '120+']
    else:
        bins = [0, 20, 50, 80, 100, 120, 150, 200, max_depth + 1]
        bin_labels = ['0-19', '20-49', '50-79', '80-99', '100-119', '120-149', '150-199', '200+']

    hist, _ = np.histogram(depths, bins=bins)
    axes[1].bar(bin_labels, hist, color='lightcoral', edgecolor='black')
    axes[1].set_xlabel('消息数量范围')
    axes[1].set_ylabel('对话数量')
    axes[1].set_title('对话深度分布 (分组)')

    # 添加数值标签
    for i, count in enumerate(hist):
        if count > 0:
            axes[1].text(i, count + max(hist) * 0.01, str(count),
                        ha='center', va='bottom')

    plt.setp(axes[1].get_xticklabels(), rotation=45, ha='right')

    # 添加统计信息
    stats_text = (f"总对话数: {depth_stats['total_conversations']}\n"
                 f"平均深度: {depth_stats['mean']:.1f}\n"
                 f"中位数深度: {depth_stats['median']:.1f}\n"
                 f"标准差: {depth_stats['std']:.1f}\n"
                 f"范围: {depth_stats['min']}-{depth_stats['max']}")

    axes[1].text(0.95, 0.95, stats_text, transform=axes[1].transAxes,
                verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

def plot_word_frequency(word_freq_user, word_freq_assistant, output_file="word_frequency.png"):
    """绘制用户和助手的词频分析图"""
    fig, axes = plt.subplots(1, 2, figsize=(20, 8))

    # 用户词频图
    if word_freq_user:
        words_user, counts_user = zip(*word_freq_user)
        axes[0].barh(range(len(words_user)), counts_user, color='skyblue')
        axes[0].set_yticks(range(len(words_user)))
        axes[0].set_yticklabels(words_user)
        axes[0].set_xlabel('频次')
        axes[0].set_title(f'用户高频词汇 (前{len(words_user)}个)')
        axes[0].invert_yaxis()

        for i, count in enumerate(counts_user):
            axes[0].text(count + max(counts_user) * 0.01, i, str(count),
                        va='center', ha='left')

    # 助手词频图
    if word_freq_assistant:
        words_assistant, counts_assistant = zip(*word_freq_assistant)
        axes[1].barh(range(len(words_assistant)), counts_assistant, color='lightgreen')
        axes[1].set_yticks(range(len(words_assistant)))
        axes[1].set_yticklabels(words_assistant)
        axes[1].set_xlabel('频次')
        axes[1].set_title(f'助手高频词汇 (前{len(words_assistant)}个)')
        axes[1].invert_yaxis()

        for i, count in enumerate(counts_assistant):
            axes[1].text(count + max(counts_assistant) * 0.01, i, str(count),
                        va='center', ha='left')

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

def plot_response_completeness(empty_responses_count, total_responses, output_file="response_completeness.png"):
    """绘制回复完整性饼图"""
    plt.figure(figsize=(8, 7))

    def autopct_format(values):
        def my_format(pct):
            total = sum(values)
            val = int(round(pct * total / 100.0))
            return '{:.1f}%\n({:d})'.format(pct, val)
        return my_format

    labels = ['空/短回复', '完整回复']
    sizes = [empty_responses_count, total_responses - empty_responses_count]
    colors = ['#ff9999', '#66b3ff']

    plt.title("助手回复完整性", fontsize=14)
    plt.pie(sizes, labels=labels, colors=colors,
           autopct=autopct_format(sizes), startangle=90,
           textprops={'fontsize': 12})
    plt.axis('equal')

    stats_text = f"总回复数: {total_responses}\n空/短回复: {empty_responses_count} ({empty_responses_count/total_responses*100:.1f}%)"

    plt.text(0, -1.3, stats_text, ha='center', fontsize=12,
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.2)
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

def main():
    parser = argparse.ArgumentParser(description="优化版对话数据统计分析工具")
    parser.add_argument("input_file", help="输入的JSON文件路径")
    parser.add_argument("--output_dir", "-o", default="./output", help="输出文件的目录路径")
    args = parser.parse_args()

    os.makedirs(args.output_dir, exist_ok=True)

    print("正在加载数据...")
    data = load_data(args.input_file)
    conversations = extract_conversations(data)

    output_base = os.path.basename(args.input_file).split('.')[0]

    print("正在分析对话轮次...")
    turns_data, min_turns, max_turns, max_turns_id = analyze_conversation_turns(conversations)
    turns_std = np.std(turns_data)
    turns_output_file = os.path.join(args.output_dir, f"{output_base}_conversation_turns.png")
    plot_conversation_turns(turns_data, output_file=turns_output_file)

    print(f"对话轮次统计: 均值={np.mean(turns_data):.2f}, 标准差={turns_std:.2f}, "
          f"中位数={np.median(turns_data):.2f}, 最小值={min_turns}, 最大值={max_turns}")

    print("正在分析消息长度...")
    user_lengths, assistant_lengths, user_min_len, user_max_len, user_max_id, assistant_min_len, assistant_max_len, assistant_max_id = analyze_message_lengths(conversations)
    user_std = np.std(user_lengths)
    assistant_std = np.std(assistant_lengths)
    lengths_output_file = os.path.join(args.output_dir, f"{output_base}_message_lengths.png")
    plot_message_lengths(user_lengths, assistant_lengths, output_file=lengths_output_file)

    print(f"用户消息长度: 均值={np.mean(user_lengths):.2f}, 标准差={user_std:.2f}, "
          f"中位数={np.median(user_lengths):.2f}, 最小值={user_min_len}, 最大值={user_max_len}")
    print(f"助手消息长度: 均值={np.mean(assistant_lengths):.2f}, 标准差={assistant_std:.2f}, "
          f"中位数={np.median(assistant_lengths):.2f}, 最小值={assistant_min_len}, 最大值={assistant_max_len}")
    print("注：消息长度统计中，英文按单词计数，中文按字符计数")

    print("正在分析对话深度...")
    depths, depth_stats = analyze_conversation_depth(conversations)
    depth_output_file = os.path.join(args.output_dir, f"{output_base}_conversation_depth.png")
    plot_conversation_depth(depths, depth_stats, output_file=depth_output_file)

    print(f"对话深度统计: 平均消息数={depth_stats['mean']:.1f}, "
          f"中位数={depth_stats['median']:.1f}, "
          f"标准差={depth_stats['std']:.1f}, "
          f"范围={depth_stats['min']}-{depth_stats['max']}")

    print("正在分析回复完整性...")
    empty_responses_count, total_responses = analyze_incomplete_responses(conversations)
    completeness_output_file = os.path.join(args.output_dir, f"{output_base}_response_completeness.png")
    plot_response_completeness(empty_responses_count, total_responses, output_file=completeness_output_file)

    print(f"回复完整性: {empty_responses_count}/{total_responses} 回复为空或过短 ({empty_responses_count/total_responses*100:.2f}%)")

    print("正在进行词频分析...")
    word_freq_user = analyze_word_frequency(conversations, role='user', top_n=20)
    word_freq_assistant = analyze_word_frequency(conversations, role='assistant', top_n=20)

    if word_freq_user or word_freq_assistant:
        word_freq_output_file = os.path.join(args.output_dir, f"{output_base}_word_frequency.png")
        plot_word_frequency(word_freq_user, word_freq_assistant, output_file=word_freq_output_file)

        if word_freq_user:
            print(f"用户高频词汇: {[word for word, _ in word_freq_user[:10]]}")
        if word_freq_assistant:
            print(f"助手高频词汇: {[word for word, _ in word_freq_assistant[:10]]}")

    print(f"\n分析完成！所有结果已保存到 {args.output_dir} 目录")

if __name__ == "__main__":
    main()
