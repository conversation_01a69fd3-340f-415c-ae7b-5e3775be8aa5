# 词频统计分析工具

这是一个用于分析JSON格式对话数据的词频统计工具。

## 功能特点

- 📊 统计基本信息（记录数、字段数）
- 🌍 整体词频分析（总词数、唯一词数、词汇丰富度）
- 🔥 高频词汇排行榜
- 🚫 智能停用词过滤
- 📝 支持中文分词

## 使用方法

### 1. 直接运行（使用默认文件）
```bash
python word_freq.py
```

### 2. 指定文件
```bash
python word_freq.py your_file.json
```

### 3. 指定显示的高频词数量
```bash
python word_freq.py --top_n 30
```

### 4. 组合使用
```bash
python word_freq.py your_file.json --top_n 30
```

## 输出示例

```
📊 基本信息:
  总记录数: 500
  发现字段数: 3

🌍 整体分析 (包含字段名):
  总词数: 1310086
  唯一词数: 49784
  词汇丰富度: 0.038

  🔥 高频词汇 (前20个):
     1. 是不是: 6143
     2. 感受: 5906
     3. 那些: 5498
     4. 担心: 4984
     5. 表达: 4924
     ...
```

## 依赖项

- jieba：中文分词库
- json：JSON数据处理
- collections：计数器功能

## 数据格式要求

输入的JSON文件应该包含对话数据，格式如下：

```json
[
  {
    "persona_id": "xxx",
    "messages": [
      {
        "role": "user",
        "content": "对话内容"
      },
      {
        "role": "assistant", 
        "content": "回复内容"
      }
    ]
  }
]
```

## 停用词处理

工具内置了丰富的中文停用词列表，包括：
- 常见虚词（的、了、和、是等）
- 标点符号
- 数字
- 对话中的高频无意义词汇

可以根据需要修改 `COMMON_STOPWORDS` 集合来调整停用词列表。
