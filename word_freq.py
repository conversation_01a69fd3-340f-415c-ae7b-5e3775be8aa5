#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
词频统计分析工具
用于分析JSON格式的对话数据中的词频分布

使用方法：
1. 直接运行（使用默认文件）：
   python word_freq.py

2. 指定文件：
   python word_freq.py your_file.json

3. 指定显示的高频词数量：
   python word_freq.py --top_n 30

4. 组合使用：
   python word_freq.py your_file.json --top_n 30
"""

import json
import jieba
import re
from collections import Counter
from typing import List, Tuple, Dict, Any
import argparse


# 停用词列表（参考anlys_diff.py）
COMMON_STOPWORDS = {
    # 原有停用词
    '觉得', '比如', '它们', '这次', '事件', '的', '了', '和', '是', '在', '我', '有', '与', '这', '他', '你', '但',
    '，', '。', '；', '：', '"', '"', '？', '！', '、', '对', '中', '从', '到', '为', '被', '用', '能', '可',
    '上', '下', '1', '2', '3', '4', '5', '以', '及', '等', '把', '就', '也', '或', '而', '很', '会', '那',
    '都', '说', '着', '将', '让', '做', '去', '还', '得', '时', '年', '月', '日',
    # 新增停用词
    '一个', '一些', '一种', '一直', '一般', '一样', '不是', '不会', '不能', '不要', '不过', '不同', '没有',
    '可以', '应该', '需要', '希望', '想要', '觉得', '认为', '表示', '显示', '出现', '发生', '进行', '开始',
    '结束', '完成', '继续', '保持', '成为', '变成', '形成', '产生', '造成', '导致', '引起', '带来', '提供',
    '获得', '得到', '拥有', '具有', '包含', '涉及', '关于', '由于', '因为', '所以', '然后', '接着', '最后',
    '首先', '其次', '另外', '此外', '同时', '然而', '但是', '虽然', '尽管', '如果', '假如', '当', '在于',
    '通过', '根据', '按照', '依据', '基于', '来自', '来源', '方面', '方式', '方法', '途径', '手段', '措施',
    '行为', '活动', '过程', '阶段', '步骤', '环节', '部分', '内容', '信息', '数据', '资料', '材料', '文件',
    '记录', '报告', '结果', '效果', '作用', '影响', '意义', '价值', '重要', '主要', '基本', '核心', '关键',
    '特别', '特殊', '具体', '详细', '明确', '清楚', '明显', '显然', '当然', '确实', '实际', '真正', '完全',
    '非常', '十分', '相当', '比较', '更加', '最为', '极其', '特别', '尤其', '特别是', '尤其是', '妈妈',
    '发现', '父母', '参加', '父亲', '经历', '班级', '同学', '分享', '同班', '知道', '能力', '擅长', '朋友',
    '复杂', '时会', '母亲', '情绪', '变化', '认知', '突然', '体会', '喜欢', '讨厌', '真诚', '', '',
    # 对话中常见的高频词汇
    '自己', '现在', '其实', '真的', '这样', '这种', '什么', '这个', '就是', '还是', '我们', '可能', '时候',
    '已经', '感觉', '这些', '用户', '心里', '轻轻', '那个', '怎么', '一下', '有点', '觉得', '想想', '看看',
    '听听', '试试', '想到', '看到', '听到', '感到', '遇到', '碰到', '遇见', '见到', '找到', '想起', '记得',
    '忘记', '记住', '想象', '理解', '明白', '知道', '了解', '清楚', '明确', '确定', '肯定', '一定', '绝对',
    '完全', '彻底', '全部', '所有', '每个', '任何', '什么', '哪个', '哪些', '怎样', '如何', '为什么', '多少',
    '几个', '一点', '有些', '许多', '很多', '不少', '大量', '少量', '一些', '部分', '全部', '整个', '全面'
}


def load_json_data(file_path: str) -> List[Dict[str, Any]]:
    """
    加载JSON数据文件

    Args:
        file_path: JSON文件路径

    Returns:
        List[Dict]: 加载的数据列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"读取JSON文件失败: {e}")
        return []


def extract_text_from_dialogues(data: List[Dict[str, Any]]) -> str:
    """
    从对话数据中提取所有文本内容

    Args:
        data: 对话数据列表

    Returns:
        str: 合并后的文本内容
    """
    all_text = []

    for dialogue in data:
        if 'messages' in dialogue:
            for message in dialogue['messages']:
                if 'content' in message:
                    content = message['content'].strip()
                    if content:
                        all_text.append(content)

    return ' '.join(all_text)


def analyze_word_frequency(text: str, top_n: int = 20) -> Tuple[List[Tuple[str, int]], int, int]:
    """
    分析文本词频

    Args:
        text: 要分析的文本
        top_n: 返回前N个高频词

    Returns:
        Tuple: (高频词列表, 总词数, 唯一词数)
    """
    # 使用jieba分词
    words = list(jieba.cut(text))

    # 过滤停用词和无意义词汇
    filtered_words = []
    for word in words:
        word = word.strip()
        if (word and
            word not in COMMON_STOPWORDS and
            len(word) > 1 and
            not word.isdigit() and  # 过滤纯数字
            not re.match(r'^[a-zA-Z]+$', word) and  # 过滤纯英文
            not re.match(r'^[^\u4e00-\u9fff]+$', word)):  # 保留包含中文的词
            filtered_words.append(word)

    # 统计词频
    word_counter = Counter(filtered_words)

    # 获取高频词
    top_words = word_counter.most_common(top_n)

    return top_words, len(filtered_words), len(word_counter)


def print_analysis_results(data: List[Dict[str, Any]], top_words: List[Tuple[str, int]],
                          total_words: int, unique_words: int):
    """
    打印分析结果

    Args:
        data: 原始数据
        top_words: 高频词列表
        total_words: 总词数
        unique_words: 唯一词数
    """
    # 计算基本信息
    total_records = len(data)
    field_count = 0

    # 统计字段数（简化处理，只统计第一条记录的字段）
    if data:
        field_count = len(data[0])
        if 'messages' in data[0] and data[0]['messages']:
            field_count += len(data[0]['messages'][0]) - 1  # 减去messages本身

    # 计算词汇丰富度
    vocabulary_richness = unique_words / total_words if total_words > 0 else 0

    print("📊 基本信息:")
    print(f"  总记录数: {total_records}")
    print(f"  发现字段数: {field_count}")
    print()

    print("🌍 整体分析 (包含字段名):")
    print(f"  总词数: {total_words}")
    print(f"  唯一词数: {unique_words}")
    print(f"  词汇丰富度: {vocabulary_richness:.3f}")
    print()

    print(f"  🔥 高频词汇 (前{len(top_words)}个):")
    for i, (word, count) in enumerate(top_words, 1):
        print(f"    {i:2d}. {word}: {count}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='词频统计分析工具')
    parser.add_argument('input_file', nargs='?', default='processed_dialogues_openai.json',
                       help='输入的JSON文件路径（默认：processed_dialogues_openai.json）')
    parser.add_argument('--top_n', type=int, default=20, help='显示前N个高频词（默认20）')

    args = parser.parse_args()

    # 加载数据
    print(f"正在加载数据文件: {args.input_file}")
    data = load_json_data(args.input_file)

    if not data:
        print("数据加载失败或为空")
        return

    # 提取文本
    print("正在提取文本内容...")
    text = extract_text_from_dialogues(data)

    if not text:
        print("未找到有效的文本内容")
        return

    # 分析词频
    print("正在进行词频分析...")
    top_words, total_words, unique_words = analyze_word_frequency(text, args.top_n)

    # 打印结果
    print("\n" + "="*50)
    print_analysis_results(data, top_words, total_words, unique_words)


if __name__ == "__main__":
    main()