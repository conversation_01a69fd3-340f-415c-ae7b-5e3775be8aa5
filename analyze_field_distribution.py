#!/usr/bin/env python3
"""
分析JSON文件中字段值的分布
统计settings中simulator和assistant字段的各个值的分布百分比
"""

import json
import sys
from collections import defaultdict, Counter
from typing import Dict, Any, List


def extract_nested_fields(data: Dict[str, Any], path: str = "") -> Dict[str, Any]:
    """递归提取嵌套字段的值"""
    result = {}
    
    for key, value in data.items():
        current_path = f"{path}.{key}" if path else key
        
        if isinstance(value, dict):
            # 递归处理嵌套字典
            nested_result = extract_nested_fields(value, current_path)
            result.update(nested_result)
        elif isinstance(value, (str, int, float, bool)):
            # 直接值
            result[current_path] = value
        elif isinstance(value, list):
            # 列表类型，转换为字符串表示
            result[current_path] = str(value)
    
    return result


def analyze_field_distribution(json_file: str) -> None:
    """分析JSON文件中字段的分布"""
    print(f"开始分析文件: {json_file}")
    
    # 用于统计各字段值的计数器
    field_counters = defaultdict(Counter)
    total_records = 0
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"文件加载成功，开始分析...")
        
        # 遍历所有记录
        for record_group in data:
            if isinstance(record_group, list):
                for record in record_group:
                    if isinstance(record, dict) and 'settings' in record:
                        total_records += 1
                        settings = record['settings']
                        
                        # 提取simulator字段
                        if 'simulator' in settings:
                            simulator_fields = extract_nested_fields(settings['simulator'], 'simulator')
                            for field_path, value in simulator_fields.items():
                                field_counters[field_path][value] += 1
                        
                        # 提取assistant字段
                        if 'assistant' in settings:
                            assistant_fields = extract_nested_fields(settings['assistant'], 'assistant')
                            for field_path, value in assistant_fields.items():
                                field_counters[field_path][value] += 1
    
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return
    
    print(f"\n总共分析了 {total_records} 条记录")
    print("=" * 80)
    
    # 输出统计结果
    for field_path in sorted(field_counters.keys()):
        print(f"\n字段: {field_path}")
        print("-" * 60)
        
        counter = field_counters[field_path]
        total_count = sum(counter.values())
        
        # 按计数排序
        for value, count in counter.most_common():
            percentage = (count / total_count) * 100
            print(f"  {value}: {count} 次 ({percentage:.2f}%)")


def main():
    """主函数"""
    json_file = "merged_batch_000-500.json"
    
    if len(sys.argv) > 1:
        json_file = sys.argv[1]
    
    analyze_field_distribution(json_file)


if __name__ == "__main__":
    main()
