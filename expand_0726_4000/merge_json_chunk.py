#!/usr/bin/env python3
"""
合并JSON chunk文件的脚本
将 mix_strategy_planned_final_assistant_dify.json._chunk_{start}_{end} 文件合并
步长为25，范围0-500，每100条记录合成一个.json文件
"""

import json
import os
from typing import List, Any


def load_chunk_file(filepath: str) -> List[Any]:
    """加载单个chunk文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载文件: {filepath}, 包含 {len(data)} 条记录")
        return data
    except Exception as e:
        print(f"加载文件失败: {filepath}, 错误: {e}")
        return []


def get_chunk_files(base_dir: str, start_range: int = 0, end_range: int = 500, step: int = 25) -> List[str]:
    """获取指定范围内的chunk文件列表"""
    chunk_files = []

    for start in range(start_range, end_range, step):
        end = start + step
        filename = f"mix_strategy_planned_final_assistant_dify.json._chunk_{start}_{end}"
        filepath = os.path.join(base_dir, filename)

        if os.path.exists(filepath):
            chunk_files.append(filepath)
        else:
            print(f"警告: 文件不存在 {filepath}")

    return chunk_files


def merge_chunks_to_batches(chunk_files: List[str], batch_size: int = 100) -> None:
    """将chunk文件合并为批次文件"""
    all_data = []

    # 加载所有chunk文件
    for chunk_file in chunk_files:
        chunk_data = load_chunk_file(chunk_file)
        all_data.extend(chunk_data)

    print(f"总共加载了 {len(all_data)} 条记录")

    # 按批次保存
    batch_num = 0
    for i in range(0, len(all_data), batch_size):
        batch_data = all_data[i:i + batch_size]
        batch_filename = f"merged_batch_{batch_num:03d}.json"

        try:
            with open(batch_filename, 'w', encoding='utf-8') as f:
                json.dump(batch_data, f, ensure_ascii=False, indent=2)

            print(f"保存批次文件: {batch_filename}, 包含 {len(batch_data)} 条记录")
            batch_num += 1

        except Exception as e:
            print(f"保存批次文件失败: {batch_filename}, 错误: {e}")


def main():
    """主函数"""
    base_dir = "."  # 当前目录

    print("开始合并JSON chunk文件...")
    print(f"范围: 0-500, 步长: 25, 每批次: 100条记录")

    # 获取chunk文件列表
    chunk_files = get_chunk_files(base_dir, start_range=0, end_range=500, step=25)

    if not chunk_files:
        print("没有找到任何chunk文件!")
        return

    print(f"找到 {len(chunk_files)} 个chunk文件")

    # 合并并保存为批次文件
    merge_chunks_to_batches(chunk_files, batch_size=500)

    print("合并完成!")


if __name__ == "__main__":
    main()