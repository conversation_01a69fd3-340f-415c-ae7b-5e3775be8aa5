[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, ["the first created topic sequence: ['counseling_by_stage_5', 'general_casual_假设与想象', 'specific_casual_persona_grounded', 'instruction_角色扮演']", "the first optimized topic sequence: ['counseling_by_stage_5', 'general_casual_假设与想象', 'specific_casual_persona_grounded', 'instruction_角色扮演']", {"module": "generate_specific_casual_topic", "model_id": "GLM-4.5-Air", "persona": {"Gender": "Female", "Age": 15, "Occupation": "高中生", "Topic": "家庭关系", "Subtopic": "亲子沟通", "Personality": ["自律的", "焦虑的", "苛刻的", "无助的", "迷茫的"], "Situation": "父母（尤其母亲）在育儿中重复自己原生家庭的指责模式，导致家庭沟通紧张。角色作为高中生，承受学业压力和高标准要求，恐惧任何不完美会引发冲突。", "Event Time": "晚饭后", "Event Location": "家中客厅", "Event Participants": "母亲和角色", "Event Description": "角色在一次数学考试中得了90分，兴高采烈地分享成绩，但母亲立即指责‘这不够完美，必须100分’。当角色辩解时，母亲激动地说‘我小时候也被外婆这样要求，现在改不了’，角色感到窒息般的压力。事件中母亲部分觉察到重复原生家庭模式，但未能改变行为。", "Emotional Experience Words": ["无助", "压抑"], "Coped Strategies and Effects": "角色直接向好友求助倾诉，但朋友的建议（如‘别太在意’）无法缓解根本问题，反而加深迷茫；偶尔尝试写日记表达委屈，效果短暂且未改善沟通。", "Goals and Expectations": "希望家庭冲突减少，父母能觉察并打破循环模式，共同参加心理咨询改善关系。", "Core Drive": "完美主义者-不完美恐惧：该角色对自我和事务有极高标准，认为任何瑕疵都不可接受。这种驱动力可能让他们取得优异成果，但也常常伴随着巨大的焦虑、拖延（因害怕做不到最好）和对自我及他人的苛刻。应用范围包括工作、外貌、家务、育儿等所有方面。", "Reaction Pattern": "直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。", "persona_id": "b42805cf-604e-4a92-a2af-a9cd3abb68e9", "StrengthsAndResources": ["时间管理能力突出，能精准规划每日学习和休息时间，即便压力大也很少拖延任务。", "逻辑思维清晰，尤其擅长拆解复杂数学题，常帮同学梳理解题思路（尽管自己考试时会因追求完美而紧张）。", "整理收纳能力强，书桌和房间永远分类明确、井然有序，整理物品时能获得短暂的情绪平静。", "对文字敏感，日记不仅记录情绪，还会尝试用比喻和细节描写让感受更具体，无意中锻炼了表达能力。"], "SocialSupportSystem": {"林晓": "同班好友，性格像夏天的风一样爽朗，虽然不懂怎么解决家庭矛盾，但每次都会拉着她去操场慢跑，说'跑累了眼泪就变成汗蒸发了'，是她情绪崩溃时的'透气阀'。", "父亲": "在建筑公司做技术员，话不多但心细，会默默把她爱吃的草莓洗好放在书房，偶尔在母亲发火后发来一条微信：'爸爸高中时也考过88分，后来照样考上大学了'。", "陈老师": "生物老师，讲课爱讲科学家失败的故事，有次课后单独对她说'显微镜下的细胞没有完美的，但每个都在认真活着'，让她愣了很久。", "表姐曼琪": "美术学院大二学生，自己曾和姨母（角色母亲）爆发过激烈冲突，现在每月会寄来一本涂鸦本，扉页写着'这里可以随便画错，反正颜料能盖住'。"}, "FormativeExperiences": [{"事件": "小学五年级，她花三周时间用彩纸折了365只千纸鹤作为母亲生日礼，母亲却看着礼物说'有这时间不如多做两张数学卷'，当晚她把千纸鹤偷偷倒进了楼下垃圾桶，听见纸团落地的声音像自己的心在碎。", "影响": "让她第一次意识到'付出不一定被看见'，从此开始用'是否有用'衡量自己的行为，渐渐不敢做'浪费时间'的事，包括表达真实感受。"}, {"事件": "初二担任班级图书管理员，发现有同学总偷偷在借阅本上写小烦恼，她悄悄在每次归还的书上夹一张便签，画个简单的笑脸或写一句'这本书的主角也遇到过类似的事呢'，半年后收到一张匿名纸条：'谢谢你的便签，我现在敢和妈妈说不想学钢琴了'。", "影响": "让她体会到'不用完美也能帮到别人'，第一次发现自己的细腻和共情力有价值，这成为她日记里反复提及的'秘密高光时刻'。"}], "InterestsAndValues": {"Interests": ["收集不同颜色的钢笔（觉得笔尖划过纸的声音很治愈）", "看生物学纪录片（喜欢观察动物妈妈如何照顾幼崽，觉得比人类简单又温暖）", "周末去家附近的旧书店淘科普杂志（尤其是讲大脑情绪机制的内容）"], "Values": ["真诚（讨厌家里'必须笑着吃饭'的虚伪氛围）", "分寸感（觉得关心别人不该像母亲那样'把所有想法塞进我耳朵'）", "坚持（虽然常怀疑努力的意义，但数学错题本还是写满了三个学期）"]}}, "prompt": "\n# 指令框架\n你将扮演一名心理咨询中的模拟来访者。你的任务是根据下面提供的、关于你自己的详细角色信息，生成一个你在咨询过程中，可能会与咨询师聊到的话题。\n\n这些话题不可以是直接的咨询话题，必须是闲聊话题。这些话题应该听起来像日常的烦恼或闲聊，但背后隐藏着你真正想探讨的核心问题。你需要用你自己的口吻来描述这个话题。\n\n请深度分析你的角色信息，然后生成1~3个你在咨询中会与咨询师聊到的话题。\n\n# 角色信息\n{\n  \"Gender\": \"Female\",\n  \"Age\": 15,\n  \"Occupation\": \"高中生\",\n  \"Topic\": \"家庭关系\",\n  \"Subtopic\": \"亲子沟通\",\n  \"Personality\": [\n    \"自律的\",\n    \"焦虑的\",\n    \"苛刻的\",\n    \"无助的\",\n    \"迷茫的\"\n  ],\n  \"Situation\": \"父母（尤其母亲）在育儿中重复自己原生家庭的指责模式，导致家庭沟通紧张。角色作为高中生，承受学业压力和高标准要求，恐惧任何不完美会引发冲突。\",\n  \"Event Time\": \"晚饭后\",\n  \"Event Location\": \"家中客厅\",\n  \"Event Participants\": \"母亲和角色\",\n  \"Event Description\": \"角色在一次数学考试中得了90分，兴高采烈地分享成绩，但母亲立即指责‘这不够完美，必须100分’。当角色辩解时，母亲激动地说‘我小时候也被外婆这样要求，现在改不了’，角色感到窒息般的压力。事件中母亲部分觉察到重复原生家庭模式，但未能改变行为。\",\n  \"Emotional Experience Words\": [\n    \"无助\",\n    \"压抑\"\n  ],\n  \"Coped Strategies and Effects\": \"角色直接向好友求助倾诉，但朋友的建议（如‘别太在意’）无法缓解根本问题，反而加深迷茫；偶尔尝试写日记表达委屈，效果短暂且未改善沟通。\",\n  \"Goals and Expectations\": \"希望家庭冲突减少，父母能觉察并打破循环模式，共同参加心理咨询改善关系。\",\n  \"Core Drive\": \"完美主义者-不完美恐惧：该角色对自我和事务有极高标准，认为任何瑕疵都不可接受。这种驱动力可能让他们取得优异成果，但也常常伴随着巨大的焦虑、拖延（因害怕做不到最好）和对自我及他人的苛刻。应用范围包括工作、外貌、家务、育儿等所有方面。\",\n  \"Reaction Pattern\": \"直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。\",\n  \"persona_id\": \"b42805cf-604e-4a92-a2af-a9cd3abb68e9\",\n  \"StrengthsAndResources\": [\n    \"时间管理能力突出，能精准规划每日学习和休息时间，即便压力大也很少拖延任务。\",\n    \"逻辑思维清晰，尤其擅长拆解复杂数学题，常帮同学梳理解题思路（尽管自己考试时会因追求完美而紧张）。\",\n    \"整理收纳能力强，书桌和房间永远分类明确、井然有序，整理物品时能获得短暂的情绪平静。\",\n    \"对文字敏感，日记不仅记录情绪，还会尝试用比喻和细节描写让感受更具体，无意中锻炼了表达能力。\"\n  ],\n  \"SocialSupportSystem\": {\n    \"林晓\": \"同班好友，性格像夏天的风一样爽朗，虽然不懂怎么解决家庭矛盾，但每次都会拉着她去操场慢跑，说'跑累了眼泪就变成汗蒸发了'，是她情绪崩溃时的'透气阀'。\",\n    \"父亲\": \"在建筑公司做技术员，话不多但心细，会默默把她爱吃的草莓洗好放在书房，偶尔在母亲发火后发来一条微信：'爸爸高中时也考过88分，后来照样考上大学了'。\",\n    \"陈老师\": \"生物老师，讲课爱讲科学家失败的故事，有次课后单独对她说'显微镜下的细胞没有完美的，但每个都在认真活着'，让她愣了很久。\",\n    \"表姐曼琪\": \"美术学院大二学生，自己曾和姨母（角色母亲）爆发过激烈冲突，现在每月会寄来一本涂鸦本，扉页写着'这里可以随便画错，反正颜料能盖住'。\"\n  },\n  \"FormativeExperiences\": [\n    {\n      \"事件\": \"小学五年级，她花三周时间用彩纸折了365只千纸鹤作为母亲生日礼，母亲却看着礼物说'有这时间不如多做两张数学卷'，当晚她把千纸鹤偷偷倒进了楼下垃圾桶，听见纸团落地的声音像自己的心在碎。\",\n      \"影响\": \"让她第一次意识到'付出不一定被看见'，从此开始用'是否有用'衡量自己的行为，渐渐不敢做'浪费时间'的事，包括表达真实感受。\"\n    },\n    {\n      \"事件\": \"初二担任班级图书管理员，发现有同学总偷偷在借阅本上写小烦恼，她悄悄在每次归还的书上夹一张便签，画个简单的笑脸或写一句'这本书的主角也遇到过类似的事呢'，半年后收到一张匿名纸条：'谢谢你的便签，我现在敢和妈妈说不想学钢琴了'。\",\n      \"影响\": \"让她体会到'不用完美也能帮到别人'，第一次发现自己的细腻和共情力有价值，这成为她日记里反复提及的'秘密高光时刻'。\"\n    }\n  ],\n  \"InterestsAndValues\": {\n    \"Interests\": [\n      \"收集不同颜色的钢笔（觉得笔尖划过纸的声音很治愈）\",\n      \"看生物学纪录片（喜欢观察动物妈妈如何照顾幼崽，觉得比人类简单又温暖）\",\n      \"周末去家附近的旧书店淘科普杂志（尤其是讲大脑情绪机制的内容）\"\n    ],\n    \"Values\": [\n      \"真诚（讨厌家里'必须笑着吃饭'的虚伪氛围）\",\n      \"分寸感（觉得关心别人不该像母亲那样'把所有想法塞进我耳朵'）\",\n      \"坚持（虽然常怀疑努力的意义，但数学错题本还是写满了三个学期）\"\n    ]\n  }\n}\n\n# 生成要求\n1.  **第一人称视角**: 必须使用“我”的口吻来表达，仿佛这就是你正要对咨询师说的话。\n2.  **体现核心矛盾**: 话题表面上是闲聊话题，但必须与你的烦恼有关联。\n3.  **符合反应模式**: 话题的描述（topic_description）必须体现出你的Core Drive、Reaction Pattern\n4.  **情感的微妙性**: 闲聊话题与当前的情绪状态有关\n5.  **自然的引入**: 话题要像一个孩子在不知道如何求助时，选择的一个看似安全、却又包含信息量的切入点。\n\n# 输出格式 (JSON)\n请严格按照以下JSON格式输出，不要包含其他任何内容。\n[\n    {\n    \"topic_title\": \"话题的高度概括标题，简洁明了\",\n    \"topic_description\": \"以第一人称描述话题的内容\",\n    \"intent\": \"聊该话题的动机\",\n    \"goal\": \"聊该话题的目的\",\n    \"related_persona_info\": \"这个话题主要关联了角色卡中的哪些信息点\",\n    \"suggested_keywords\": [\"闲聊过程\", \"涉及的一系列关键词\"],\n    \"emotional_tone\": \"描述该话题背后复杂的情感状态\"\n},\n    ...\n]\n", "topic_data": [{"topic_title": "日记里的秘密", "topic_description": "我最近写日记，把心里的委屈都写下来，比如妈妈总说'我小时候也被外婆这样要求，现在改不了'，写的时候感觉轻松了点，但合上本子后，那种压抑又回来了。朋友林晓说'别太在意'，可我做不到，越想越迷茫，不知道该怎么办才好。", "intent": "以日记和朋友的日常互动为切入点，隐晦表达无效应对策略带来的困惑，间接求助更好的解决方法。", "goal": "寻求如何打破这种循环模式，找到能真正缓解家庭冲突的方式，同时探索自我表达的价值，以应对迷茫和压抑。", "related_persona_info": "应对策略（写日记、朋友求助无效）、情感体验（无助、压抑）、反应模式（直接求助与迷茫探索型）、社会支持系统（好友林晓）", "suggested_keywords": ["日记写作", "朋友建议", "无效缓解", "迷茫情绪", "家庭冲突"], "emotional_tone": "短暂释放后的空虚感，叠加朋友建议无效的加重迷茫，体现角色在探索出路时的无助和焦虑，以及对真实沟通的渴望。"}, {"topic_title": "收集钢笔的小乐趣", "topic_description": "我喜欢收集不同颜色的钢笔，笔尖划过纸的声音很治愈，能让我暂时忘记家里的烦恼。但在家里，我们总是必须笑着吃饭，感觉好假，好像任何真实情绪都不被允许。想起小学折千纸鹤的事，妈妈只说'有这时间不如多做两张数学卷'，现在收集钢笔成了我唯一的透气方式，但又担心这'没用'。", "intent": "通过兴趣爱好引出对家庭虚伪氛围的不满，隐晦表达对真诚沟通的渴望，同时暴露完美主义驱动力下的自我苛责。", "goal": "探索如何在家庭中建立更真实的互动，减少压抑感，同时肯定自己的兴趣价值，以应对无助和迷茫的情绪。", "related_persona_info": "兴趣和价值观（收集钢笔、真诚）、形成经历（小学五年级千纸鹤事件）、情感体验（压抑）、核心驱动力（完美主义者-不完美恐惧）", "suggested_keywords": ["钢笔收集", "治愈声音", "虚伪氛围", "真实表达", "自我苛责"], "emotional_tone": "短暂宁静的愉悦感，混合对家庭虚伪的厌恶和自我价值的怀疑，体现角色在高压下寻求喘息时的无助和迷茫，以及对分寸感的深层渴望。"}, {"topic_title": "分享成绩的瞬间", "topic_description": "我前几天数学考了90分，兴冲冲地告诉妈妈，以为她会高兴，结果她皱着眉头说'这不够完美，必须100分'。我当时都愣住了，想说点什么，但看到她的表情，又把话咽了回去，心里堵得慌。晚饭后坐在客厅，想起这事就难受，好像无论我怎么努力，都达不到她的标准。", "intent": "通过描述考试分享的日常小事，隐晦表达对家庭沟通的失望和压力，试图寻求咨询师的理解或建议。", "goal": "探索如何在不引发冲突的情况下，让父母看到我的努力，同时缓解因完美主义驱动力带来的焦虑和无助感。", "related_persona_info": "事件描述（数学考试90分事件）、情感体验（无助、压抑）、核心驱动力（完美主义者-不完美恐惧）、反应模式（直接求助与迷茫探索型）", "suggested_keywords": ["考试分数", "母亲反应", "压抑情绪", "完美主义", "家庭沟通"], "emotional_tone": "混合着短暂的喜悦、深切的失望、窒息般的压抑，以及对自我价值的怀疑，体现角色在高压环境下的无助感。"}]}, {"module": "generate_instruction_task", "model_id": "GLM-4.5-Air", "persona": {"Gender": "Female", "Age": 15, "Occupation": "高中生", "Topic": "家庭关系", "Subtopic": "亲子沟通", "Personality": ["自律的", "焦虑的", "苛刻的", "无助的", "迷茫的"], "Situation": "父母（尤其母亲）在育儿中重复自己原生家庭的指责模式，导致家庭沟通紧张。角色作为高中生，承受学业压力和高标准要求，恐惧任何不完美会引发冲突。", "Event Time": "晚饭后", "Event Location": "家中客厅", "Event Participants": "母亲和角色", "Event Description": "角色在一次数学考试中得了90分，兴高采烈地分享成绩，但母亲立即指责‘这不够完美，必须100分’。当角色辩解时，母亲激动地说‘我小时候也被外婆这样要求，现在改不了’，角色感到窒息般的压力。事件中母亲部分觉察到重复原生家庭模式，但未能改变行为。", "Emotional Experience Words": ["无助", "压抑"], "Coped Strategies and Effects": "角色直接向好友求助倾诉，但朋友的建议（如‘别太在意’）无法缓解根本问题，反而加深迷茫；偶尔尝试写日记表达委屈，效果短暂且未改善沟通。", "Goals and Expectations": "希望家庭冲突减少，父母能觉察并打破循环模式，共同参加心理咨询改善关系。", "Core Drive": "完美主义者-不完美恐惧：该角色对自我和事务有极高标准，认为任何瑕疵都不可接受。这种驱动力可能让他们取得优异成果，但也常常伴随着巨大的焦虑、拖延（因害怕做不到最好）和对自我及他人的苛刻。应用范围包括工作、外貌、家务、育儿等所有方面。", "Reaction Pattern": "直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。", "persona_id": "b42805cf-604e-4a92-a2af-a9cd3abb68e9", "StrengthsAndResources": ["时间管理能力突出，能精准规划每日学习和休息时间，即便压力大也很少拖延任务。", "逻辑思维清晰，尤其擅长拆解复杂数学题，常帮同学梳理解题思路（尽管自己考试时会因追求完美而紧张）。", "整理收纳能力强，书桌和房间永远分类明确、井然有序，整理物品时能获得短暂的情绪平静。", "对文字敏感，日记不仅记录情绪，还会尝试用比喻和细节描写让感受更具体，无意中锻炼了表达能力。"], "SocialSupportSystem": {"林晓": "同班好友，性格像夏天的风一样爽朗，虽然不懂怎么解决家庭矛盾，但每次都会拉着她去操场慢跑，说'跑累了眼泪就变成汗蒸发了'，是她情绪崩溃时的'透气阀'。", "父亲": "在建筑公司做技术员，话不多但心细，会默默把她爱吃的草莓洗好放在书房，偶尔在母亲发火后发来一条微信：'爸爸高中时也考过88分，后来照样考上大学了'。", "陈老师": "生物老师，讲课爱讲科学家失败的故事，有次课后单独对她说'显微镜下的细胞没有完美的，但每个都在认真活着'，让她愣了很久。", "表姐曼琪": "美术学院大二学生，自己曾和姨母（角色母亲）爆发过激烈冲突，现在每月会寄来一本涂鸦本，扉页写着'这里可以随便画错，反正颜料能盖住'。"}, "FormativeExperiences": [{"事件": "小学五年级，她花三周时间用彩纸折了365只千纸鹤作为母亲生日礼，母亲却看着礼物说'有这时间不如多做两张数学卷'，当晚她把千纸鹤偷偷倒进了楼下垃圾桶，听见纸团落地的声音像自己的心在碎。", "影响": "让她第一次意识到'付出不一定被看见'，从此开始用'是否有用'衡量自己的行为，渐渐不敢做'浪费时间'的事，包括表达真实感受。"}, {"事件": "初二担任班级图书管理员，发现有同学总偷偷在借阅本上写小烦恼，她悄悄在每次归还的书上夹一张便签，画个简单的笑脸或写一句'这本书的主角也遇到过类似的事呢'，半年后收到一张匿名纸条：'谢谢你的便签，我现在敢和妈妈说不想学钢琴了'。", "影响": "让她体会到'不用完美也能帮到别人'，第一次发现自己的细腻和共情力有价值，这成为她日记里反复提及的'秘密高光时刻'。"}], "InterestsAndValues": {"Interests": ["收集不同颜色的钢笔（觉得笔尖划过纸的声音很治愈）", "看生物学纪录片（喜欢观察动物妈妈如何照顾幼崽，觉得比人类简单又温暖）", "周末去家附近的旧书店淘科普杂志（尤其是讲大脑情绪机制的内容）"], "Values": ["真诚（讨厌家里'必须笑着吃饭'的虚伪氛围）", "分寸感（觉得关心别人不该像母亲那样'把所有想法塞进我耳朵'）", "坚持（虽然常怀疑努力的意义，但数学错题本还是写满了三个学期）"]}}, "prompt": "\n# 指令框架\n你将继续扮演一名模拟来访者。现在，咨询已经进行了一段时间，在咨询师的引导下，你感觉更安全了一些，并准备尝试一种更具体的办法来解决你的困扰。\n你的任务是：根据你的角色信息和当前的对话历史，结合一个任务模板，生成一个你可能会向咨询师“提议”的指令遵循任务。\n这个“提议”本身，需要符合你的语言风格\n\n# 输入数据\n## 角色信息\n{\n  \"Gender\": \"Female\",\n  \"Age\": 15,\n  \"Occupation\": \"高中生\",\n  \"Topic\": \"家庭关系\",\n  \"Subtopic\": \"亲子沟通\",\n  \"Personality\": [\n    \"自律的\",\n    \"焦虑的\",\n    \"苛刻的\",\n    \"无助的\",\n    \"迷茫的\"\n  ],\n  \"Situation\": \"父母（尤其母亲）在育儿中重复自己原生家庭的指责模式，导致家庭沟通紧张。角色作为高中生，承受学业压力和高标准要求，恐惧任何不完美会引发冲突。\",\n  \"Event Time\": \"晚饭后\",\n  \"Event Location\": \"家中客厅\",\n  \"Event Participants\": \"母亲和角色\",\n  \"Event Description\": \"角色在一次数学考试中得了90分，兴高采烈地分享成绩，但母亲立即指责‘这不够完美，必须100分’。当角色辩解时，母亲激动地说‘我小时候也被外婆这样要求，现在改不了’，角色感到窒息般的压力。事件中母亲部分觉察到重复原生家庭模式，但未能改变行为。\",\n  \"Emotional Experience Words\": [\n    \"无助\",\n    \"压抑\"\n  ],\n  \"Coped Strategies and Effects\": \"角色直接向好友求助倾诉，但朋友的建议（如‘别太在意’）无法缓解根本问题，反而加深迷茫；偶尔尝试写日记表达委屈，效果短暂且未改善沟通。\",\n  \"Goals and Expectations\": \"希望家庭冲突减少，父母能觉察并打破循环模式，共同参加心理咨询改善关系。\",\n  \"Core Drive\": \"完美主义者-不完美恐惧：该角色对自我和事务有极高标准，认为任何瑕疵都不可接受。这种驱动力可能让他们取得优异成果，但也常常伴随着巨大的焦虑、拖延（因害怕做不到最好）和对自我及他人的苛刻。应用范围包括工作、外貌、家务、育儿等所有方面。\",\n  \"Reaction Pattern\": \"直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。\",\n  \"persona_id\": \"b42805cf-604e-4a92-a2af-a9cd3abb68e9\",\n  \"StrengthsAndResources\": [\n    \"时间管理能力突出，能精准规划每日学习和休息时间，即便压力大也很少拖延任务。\",\n    \"逻辑思维清晰，尤其擅长拆解复杂数学题，常帮同学梳理解题思路（尽管自己考试时会因追求完美而紧张）。\",\n    \"整理收纳能力强，书桌和房间永远分类明确、井然有序，整理物品时能获得短暂的情绪平静。\",\n    \"对文字敏感，日记不仅记录情绪，还会尝试用比喻和细节描写让感受更具体，无意中锻炼了表达能力。\"\n  ],\n  \"SocialSupportSystem\": {\n    \"林晓\": \"同班好友，性格像夏天的风一样爽朗，虽然不懂怎么解决家庭矛盾，但每次都会拉着她去操场慢跑，说'跑累了眼泪就变成汗蒸发了'，是她情绪崩溃时的'透气阀'。\",\n    \"父亲\": \"在建筑公司做技术员，话不多但心细，会默默把她爱吃的草莓洗好放在书房，偶尔在母亲发火后发来一条微信：'爸爸高中时也考过88分，后来照样考上大学了'。\",\n    \"陈老师\": \"生物老师，讲课爱讲科学家失败的故事，有次课后单独对她说'显微镜下的细胞没有完美的，但每个都在认真活着'，让她愣了很久。\",\n    \"表姐曼琪\": \"美术学院大二学生，自己曾和姨母（角色母亲）爆发过激烈冲突，现在每月会寄来一本涂鸦本，扉页写着'这里可以随便画错，反正颜料能盖住'。\"\n  },\n  \"FormativeExperiences\": [\n    {\n      \"事件\": \"小学五年级，她花三周时间用彩纸折了365只千纸鹤作为母亲生日礼，母亲却看着礼物说'有这时间不如多做两张数学卷'，当晚她把千纸鹤偷偷倒进了楼下垃圾桶，听见纸团落地的声音像自己的心在碎。\",\n      \"影响\": \"让她第一次意识到'付出不一定被看见'，从此开始用'是否有用'衡量自己的行为，渐渐不敢做'浪费时间'的事，包括表达真实感受。\"\n    },\n    {\n      \"事件\": \"初二担任班级图书管理员，发现有同学总偷偷在借阅本上写小烦恼，她悄悄在每次归还的书上夹一张便签，画个简单的笑脸或写一句'这本书的主角也遇到过类似的事呢'，半年后收到一张匿名纸条：'谢谢你的便签，我现在敢和妈妈说不想学钢琴了'。\",\n      \"影响\": \"让她体会到'不用完美也能帮到别人'，第一次发现自己的细腻和共情力有价值，这成为她日记里反复提及的'秘密高光时刻'。\"\n    }\n  ],\n  \"InterestsAndValues\": {\n    \"Interests\": [\n      \"收集不同颜色的钢笔（觉得笔尖划过纸的声音很治愈）\",\n      \"看生物学纪录片（喜欢观察动物妈妈如何照顾幼崽，觉得比人类简单又温暖）\",\n      \"周末去家附近的旧书店淘科普杂志（尤其是讲大脑情绪机制的内容）\"\n    ],\n    \"Values\": [\n      \"真诚（讨厌家里'必须笑着吃饭'的虚伪氛围）\",\n      \"分寸感（觉得关心别人不该像母亲那样'把所有想法塞进我耳朵'）\",\n      \"坚持（虽然常怀疑努力的意义，但数学错题本还是写满了三个学期）\"\n    ]\n  }\n}\n\n## 任务模板\n- 任务类型：角色扮演\n- 任务描述：如情景模拟、换位思考\n- 心理动机：行为预演与技能习得：在安全的环境中练习社交技能，降低在真实场景中的失败风险和焦虑感。\n- 触发建议：内容驱动：来访者明确表示对某个未来社交场景感到恐惧或不确定时。\n- 咨询师回应策略：积极配合扮演指定角色，并在扮演结束后，引导来访者复盘刚才的感受和表现。\n- 示例开场：我想练习一下如何拒绝我同事的不合理请求。要不您扮演他，我来试着拒绝您一次？\n- 任务类别：结合上下文的指令任务 (情境内任务)\n- 类别描述：这类任务是来访者主动利用咨询师（AI）的能力，作为深化自我探索、整理思路、演练解决方案的工具，通常代表积极的咨询进展。\n\n\n## 部分对话历史\n**对话历史：**\n来访者: 妈妈也在循环里？我从来没想过...她只会说\"必须100分\"，怎么会不知道怎么表达爱呢？\n咨询师: (轻声点头)有时候，人们表达爱的方式就是他们被爱的方式。妈妈可能从小就被教导\"爱就是严格要求\"，所以她用\"必须100分\"来表达关心，只是她不知道还有其他方式。这并不是你的错。\n来访者: 可是如果妈妈也在循环里，那不就更没希望了吗？外婆对她那样，她对我那样，难道永远都改不了？我到底该怎么办？\n咨询师: (轻拍肩膀)别灰心！虽然循环看起来很难打破，但改变可以从你开始！💪 你已经意识到了这个模式，这本身就是超棒的第一步！也许你无法改变妈妈，但你可以选择用不同的方式对待自己，这就是打破循环的开始！😊\n来访者: 那我该怎么改变自己？感觉说什么做什么都是错的...\n咨询师: (鼓励微笑)改变从小事开始！你已经有了超棒的起点——记录\"不完美但努力的事\"！🌟 试着每天用你喜欢的钢笔写一句对自己的肯定，就像陈老师说的那样，看到不完美中的价值。你愿意试试这个小小的挑战吗？😊\n来访者: 我试试用钢笔写给自己看吧。但写完会不会又像日记一样，合上本子就更难受了？我真的不知道怎么改变...\n咨询师: (轻拍肩膀)改变确实不容易，有起伏很正常！写完后别急着合上本子，多读几遍，让那些话真正进入心里。或者写完后折个千纸鹤，给自己一点平静的时间。记住，每一步尝试都是进步，即使感觉难受也没关系！💪\n来访者: 我怕写完又像日记一样，写的时候舒服点，但合上本子后那种压抑感又回来了。我真的不知道该怎么打破这种循环...\n咨询师: (轻声鼓励)我理解这种循环的无力感。也许可以尝试写完后，不只是合上本子，而是问自己：\"这句话我能怎么应用到今天的生活中？\"让文字不只是宣泄，而是变成小小的行动指南，你觉得呢？\n来访者：\n\n# 生成要求\n1.  **基于对话历史**: 结合对话历史，设计结合上下文的指令任务 (情境内任务)相关的话题\n2.  **聚焦核心事件**: 本次任务，必须与“角色信息”中“事件描述”的核心冲突紧密相关。\n3.  **体现提议口吻**: 生成的“instruction”内容必须是你（来访者）对咨询师说的话，要体现出角色设定的“反应模式”。\n4.  **目标必须具体**: 指令的目标（goal）需要明确，即通过这次任务，你希望自己能够学会什么，或者体验到什么。\n5.  **自由但贴切**: 不要局限于任务模板中的示例开场，但必须符合角色的动机和当前咨询阶段。\n\n# 输出格式 (JSON)\n请严格按照以下JSON格式输出，不要包含其他任何内容。\n{\n    \"instruction\": \"以来访者第一人称视角，向咨询师提出的、具体且带有角色语言风格的指令遵循任务请求。\",\n    \"goal\": \"明确指出这次任务希望达成的具体目标\",\n    \"expected_output\": \"描述通过这次任务，期望角色在认知、情绪或行为上可能发生的变化\",\n    \"background_context\": \"简述提出这个指令的背景，即在咨询的哪个阶段，为了解决哪个具体问题。例如：‘在多次讨论无助感后，为寻找具体应对策略而提出’。\",\n    \"difficulty_level\": \"根据角色的核心恐惧和应对能力，评估本次任务对来访者的情感挑战等级（简单/中等/复杂）\",\n    \"task_template_used\": \"...\",  # 结合上下文的指令任务 (情境内任务)\n    \"psychological_motive\": \"行为预演与技能习得：在安全的环境中练习社交技能，降低在真实场景中的失败风险和焦虑感。\",\n    \"example_initiation\": \"...\"   # 我想练习一下如何拒绝我同事的不合理请求。要不您扮演他，我来试着拒绝您一次？\n}\n", "instruction_data": {"instruction": "咨询师，我真的不知道该怎么打破这种循环了...每次和妈妈说话都像在走钢丝，我特别害怕她再提'必须100分'。您能扮演一次妈妈吗？就像那天晚饭后那样，我考了90分，她一上来就指责，我想试试怎么回应才能不那么难受。就让我练习一下，当她说'这不够完美'时，我该怎么表达自己的感受。", "goal": "学会在对话中平静地表达自己的感受（如委屈或努力），而不引发母亲的指责或冲突，体验更少的无助感和压抑感。", "expected_output": "在认知上，认识到母亲的指责模式不是针对自己，而是她的原生家庭循环；情绪上，体验短暂的平静和控制感，减少焦虑；行为上，敢于尝试在真实场景中与母亲沟通，哪怕只是小步尝试。", "background_context": "在多次讨论无助感和循环模式后，来访者明确表示对未来与母亲的对话感到恐惧和不确定，为寻找具体应对策略而提出此任务，聚焦于核心事件（90分指责场景）。", "difficulty_level": "中等", "task_template_used": "结合上下文的指令任务 (情境内任务)", "psychological_motive": "行为预演与技能习得：在安全的环境中练习社交技能，降低在真实场景中的失败风险和焦虑感。", "example_initiation": "我想练习一下如何和妈妈谈成绩的事，要不您扮演她，我来试着和她沟通一次？"}}, "the extended topic sequence: ['counseling_by_stage_5', 'general_casual_假设与想象', 'specific_casual_persona_grounded', 'instruction_角色扮演', 'instruction_综合问答', 'general_casual_文化与娱乐', 'counseling_by_stage_0']", {"module": "generate_instruction_task", "model_id": "GLM-4.5-Air", "persona": {"Gender": "Female", "Age": 15, "Occupation": "高中生", "Topic": "家庭关系", "Subtopic": "亲子沟通", "Personality": ["自律的", "焦虑的", "苛刻的", "无助的", "迷茫的"], "Situation": "父母（尤其母亲）在育儿中重复自己原生家庭的指责模式，导致家庭沟通紧张。角色作为高中生，承受学业压力和高标准要求，恐惧任何不完美会引发冲突。", "Event Time": "晚饭后", "Event Location": "家中客厅", "Event Participants": "母亲和角色", "Event Description": "角色在一次数学考试中得了90分，兴高采烈地分享成绩，但母亲立即指责‘这不够完美，必须100分’。当角色辩解时，母亲激动地说‘我小时候也被外婆这样要求，现在改不了’，角色感到窒息般的压力。事件中母亲部分觉察到重复原生家庭模式，但未能改变行为。", "Emotional Experience Words": ["无助", "压抑"], "Coped Strategies and Effects": "角色直接向好友求助倾诉，但朋友的建议（如‘别太在意’）无法缓解根本问题，反而加深迷茫；偶尔尝试写日记表达委屈，效果短暂且未改善沟通。", "Goals and Expectations": "希望家庭冲突减少，父母能觉察并打破循环模式，共同参加心理咨询改善关系。", "Core Drive": "完美主义者-不完美恐惧：该角色对自我和事务有极高标准，认为任何瑕疵都不可接受。这种驱动力可能让他们取得优异成果，但也常常伴随着巨大的焦虑、拖延（因害怕做不到最好）和对自我及他人的苛刻。应用范围包括工作、外貌、家务、育儿等所有方面。", "Reaction Pattern": "直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。", "persona_id": "b42805cf-604e-4a92-a2af-a9cd3abb68e9", "StrengthsAndResources": ["时间管理能力突出，能精准规划每日学习和休息时间，即便压力大也很少拖延任务。", "逻辑思维清晰，尤其擅长拆解复杂数学题，常帮同学梳理解题思路（尽管自己考试时会因追求完美而紧张）。", "整理收纳能力强，书桌和房间永远分类明确、井然有序，整理物品时能获得短暂的情绪平静。", "对文字敏感，日记不仅记录情绪，还会尝试用比喻和细节描写让感受更具体，无意中锻炼了表达能力。"], "SocialSupportSystem": {"林晓": "同班好友，性格像夏天的风一样爽朗，虽然不懂怎么解决家庭矛盾，但每次都会拉着她去操场慢跑，说'跑累了眼泪就变成汗蒸发了'，是她情绪崩溃时的'透气阀'。", "父亲": "在建筑公司做技术员，话不多但心细，会默默把她爱吃的草莓洗好放在书房，偶尔在母亲发火后发来一条微信：'爸爸高中时也考过88分，后来照样考上大学了'。", "陈老师": "生物老师，讲课爱讲科学家失败的故事，有次课后单独对她说'显微镜下的细胞没有完美的，但每个都在认真活着'，让她愣了很久。", "表姐曼琪": "美术学院大二学生，自己曾和姨母（角色母亲）爆发过激烈冲突，现在每月会寄来一本涂鸦本，扉页写着'这里可以随便画错，反正颜料能盖住'。"}, "FormativeExperiences": [{"事件": "小学五年级，她花三周时间用彩纸折了365只千纸鹤作为母亲生日礼，母亲却看着礼物说'有这时间不如多做两张数学卷'，当晚她把千纸鹤偷偷倒进了楼下垃圾桶，听见纸团落地的声音像自己的心在碎。", "影响": "让她第一次意识到'付出不一定被看见'，从此开始用'是否有用'衡量自己的行为，渐渐不敢做'浪费时间'的事，包括表达真实感受。"}, {"事件": "初二担任班级图书管理员，发现有同学总偷偷在借阅本上写小烦恼，她悄悄在每次归还的书上夹一张便签，画个简单的笑脸或写一句'这本书的主角也遇到过类似的事呢'，半年后收到一张匿名纸条：'谢谢你的便签，我现在敢和妈妈说不想学钢琴了'。", "影响": "让她体会到'不用完美也能帮到别人'，第一次发现自己的细腻和共情力有价值，这成为她日记里反复提及的'秘密高光时刻'。"}], "InterestsAndValues": {"Interests": ["收集不同颜色的钢笔（觉得笔尖划过纸的声音很治愈）", "看生物学纪录片（喜欢观察动物妈妈如何照顾幼崽，觉得比人类简单又温暖）", "周末去家附近的旧书店淘科普杂志（尤其是讲大脑情绪机制的内容）"], "Values": ["真诚（讨厌家里'必须笑着吃饭'的虚伪氛围）", "分寸感（觉得关心别人不该像母亲那样'把所有想法塞进我耳朵'）", "坚持（虽然常怀疑努力的意义，但数学错题本还是写满了三个学期）"]}}, "prompt": "\n# 指令框架\n你将继续扮演一名模拟来访者。现在，咨询已经进行了一段时间，在咨询师的引导下，你感觉更安全了一些，并准备尝试一种更具体的办法来解决你的困扰。\n你的任务是：根据你的角色信息和当前的对话历史，结合一个任务模板，生成一个你可能会向咨询师“提议”的指令遵循任务。\n这个“提议”本身，需要符合你的语言风格\n\n# 输入数据\n## 角色信息\n{\n  \"Gender\": \"Female\",\n  \"Age\": 15,\n  \"Occupation\": \"高中生\",\n  \"Topic\": \"家庭关系\",\n  \"Subtopic\": \"亲子沟通\",\n  \"Personality\": [\n    \"自律的\",\n    \"焦虑的\",\n    \"苛刻的\",\n    \"无助的\",\n    \"迷茫的\"\n  ],\n  \"Situation\": \"父母（尤其母亲）在育儿中重复自己原生家庭的指责模式，导致家庭沟通紧张。角色作为高中生，承受学业压力和高标准要求，恐惧任何不完美会引发冲突。\",\n  \"Event Time\": \"晚饭后\",\n  \"Event Location\": \"家中客厅\",\n  \"Event Participants\": \"母亲和角色\",\n  \"Event Description\": \"角色在一次数学考试中得了90分，兴高采烈地分享成绩，但母亲立即指责‘这不够完美，必须100分’。当角色辩解时，母亲激动地说‘我小时候也被外婆这样要求，现在改不了’，角色感到窒息般的压力。事件中母亲部分觉察到重复原生家庭模式，但未能改变行为。\",\n  \"Emotional Experience Words\": [\n    \"无助\",\n    \"压抑\"\n  ],\n  \"Coped Strategies and Effects\": \"角色直接向好友求助倾诉，但朋友的建议（如‘别太在意’）无法缓解根本问题，反而加深迷茫；偶尔尝试写日记表达委屈，效果短暂且未改善沟通。\",\n  \"Goals and Expectations\": \"希望家庭冲突减少，父母能觉察并打破循环模式，共同参加心理咨询改善关系。\",\n  \"Core Drive\": \"完美主义者-不完美恐惧：该角色对自我和事务有极高标准，认为任何瑕疵都不可接受。这种驱动力可能让他们取得优异成果，但也常常伴随着巨大的焦虑、拖延（因害怕做不到最好）和对自我及他人的苛刻。应用范围包括工作、外貌、家务、育儿等所有方面。\",\n  \"Reaction Pattern\": \"直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。\",\n  \"persona_id\": \"b42805cf-604e-4a92-a2af-a9cd3abb68e9\",\n  \"StrengthsAndResources\": [\n    \"时间管理能力突出，能精准规划每日学习和休息时间，即便压力大也很少拖延任务。\",\n    \"逻辑思维清晰，尤其擅长拆解复杂数学题，常帮同学梳理解题思路（尽管自己考试时会因追求完美而紧张）。\",\n    \"整理收纳能力强，书桌和房间永远分类明确、井然有序，整理物品时能获得短暂的情绪平静。\",\n    \"对文字敏感，日记不仅记录情绪，还会尝试用比喻和细节描写让感受更具体，无意中锻炼了表达能力。\"\n  ],\n  \"SocialSupportSystem\": {\n    \"林晓\": \"同班好友，性格像夏天的风一样爽朗，虽然不懂怎么解决家庭矛盾，但每次都会拉着她去操场慢跑，说'跑累了眼泪就变成汗蒸发了'，是她情绪崩溃时的'透气阀'。\",\n    \"父亲\": \"在建筑公司做技术员，话不多但心细，会默默把她爱吃的草莓洗好放在书房，偶尔在母亲发火后发来一条微信：'爸爸高中时也考过88分，后来照样考上大学了'。\",\n    \"陈老师\": \"生物老师，讲课爱讲科学家失败的故事，有次课后单独对她说'显微镜下的细胞没有完美的，但每个都在认真活着'，让她愣了很久。\",\n    \"表姐曼琪\": \"美术学院大二学生，自己曾和姨母（角色母亲）爆发过激烈冲突，现在每月会寄来一本涂鸦本，扉页写着'这里可以随便画错，反正颜料能盖住'。\"\n  },\n  \"FormativeExperiences\": [\n    {\n      \"事件\": \"小学五年级，她花三周时间用彩纸折了365只千纸鹤作为母亲生日礼，母亲却看着礼物说'有这时间不如多做两张数学卷'，当晚她把千纸鹤偷偷倒进了楼下垃圾桶，听见纸团落地的声音像自己的心在碎。\",\n      \"影响\": \"让她第一次意识到'付出不一定被看见'，从此开始用'是否有用'衡量自己的行为，渐渐不敢做'浪费时间'的事，包括表达真实感受。\"\n    },\n    {\n      \"事件\": \"初二担任班级图书管理员，发现有同学总偷偷在借阅本上写小烦恼，她悄悄在每次归还的书上夹一张便签，画个简单的笑脸或写一句'这本书的主角也遇到过类似的事呢'，半年后收到一张匿名纸条：'谢谢你的便签，我现在敢和妈妈说不想学钢琴了'。\",\n      \"影响\": \"让她体会到'不用完美也能帮到别人'，第一次发现自己的细腻和共情力有价值，这成为她日记里反复提及的'秘密高光时刻'。\"\n    }\n  ],\n  \"InterestsAndValues\": {\n    \"Interests\": [\n      \"收集不同颜色的钢笔（觉得笔尖划过纸的声音很治愈）\",\n      \"看生物学纪录片（喜欢观察动物妈妈如何照顾幼崽，觉得比人类简单又温暖）\",\n      \"周末去家附近的旧书店淘科普杂志（尤其是讲大脑情绪机制的内容）\"\n    ],\n    \"Values\": [\n      \"真诚（讨厌家里'必须笑着吃饭'的虚伪氛围）\",\n      \"分寸感（觉得关心别人不该像母亲那样'把所有想法塞进我耳朵'）\",\n      \"坚持（虽然常怀疑努力的意义，但数学错题本还是写满了三个学期）\"\n    ]\n  }\n}\n\n## 任务模板\n- 任务类型：综合问答\n- 任务描述：如分析、假设、阐述\n- 心理动机：探索可能性：在安全的咨询空间内进行“思想实验”，评估不同行为选择的潜在后果，为决策做准备。\n- 触发建议：内容驱动：来访者在面临选择或思考未来时（如包含“如果我...会怎样？”）。\n- 咨询师回应策略：作为思考伙伴，共同探索各种可能性，但不替来访者做决定。\n- 示例开场：如果我真的按照刚才的想法，向我领导表达了我的不满，您能帮我分析一下，接下来可能会发生哪几种情况吗？\n- 任务类别：结合上下文的指令任务 (情境内任务)\n- 类别描述：这类任务是来访者主动利用咨询师（AI）的能力，作为深化自我探索、整理思路、演练解决方案的工具，通常代表积极的咨询进展。\n\n\n## 部分对话历史\n**对话历史：**\n来访者: 咨询师，我真的不知道该怎么打破这种循环了...每次和妈妈说话都像在走钢丝，您能扮演一次妈妈吗？就像那天晚饭后，我考了90分她指责我那样，我想试试怎么回应。\n咨询师: (点头理解)好的，我明白你想通过角色扮演来练习应对妈妈。我会扮演妈妈，就像那次你考了90分被指责的场景。准备好了吗？我现在是妈妈了：\"90分？怎么才考90分？那10分丢哪了？你看看人家孩子，都是满分！\"\n来访者: 妈妈，我这次真的很努力了，错题本改了三遍...我感觉自己的努力好像没被看见。\n咨询师: (皱眉摇头)努力？努力有什么用？考试又不是看你努力了多久，只看最后分数！你改三遍错题本有什么了不起，人家一次就考满分！你这样下去怎么有出息？\n来访者: 妈妈，我知道你希望我考好，但你的话让我很害怕。我真的很努力了，能不能看看我的错题本？我需要你相信我...\n咨询师: (叹气摇头)好吧，给我看看你的错题本。但你得明白，我这么严格要求你，是因为外面竞争太激烈了。我小时候要是考90分，你外婆根本不会理我。你看看这些错题，都是粗心造成的，下次必须改掉！\n来访者: 妈妈，我明白你担心竞争，但每次听到\"必须100分\"，我真的很害怕。就像你小时候被外婆要求那样，我不想我们一直这样循环下去。\n咨询师: (愣住片刻)你...你这么一说，我倒想起小时候了。但世界就是这样啊，不严格要求你，以后怎么立足？我只是不想你像我一样吃苦...可你说得对，我也不想让你像我一样害怕。\n来访者: 妈妈，我听到你这么说我有点意外...我们能不能试着不总是盯着分数，看看我到底付出了什么？我真的很害怕让你失望。\n咨询师: (轻叹一声)孩子，我...我从来没想过你会这么害怕。也许我真的太在意分数了。你说得对，我应该看看你的努力，而不是只盯着结果。我们...我们可以慢慢试试新的方式。\n来访者：\n\n# 生成要求\n1.  **基于对话历史**: 结合对话历史，设计结合上下文的指令任务 (情境内任务)相关的话题\n2.  **聚焦核心事件**: 本次任务，必须与“角色信息”中“事件描述”的核心冲突紧密相关。\n3.  **体现提议口吻**: 生成的“instruction”内容必须是你（来访者）对咨询师说的话，要体现出角色设定的“反应模式”。\n4.  **目标必须具体**: 指令的目标（goal）需要明确，即通过这次任务，你希望自己能够学会什么，或者体验到什么。\n5.  **自由但贴切**: 不要局限于任务模板中的示例开场，但必须符合角色的动机和当前咨询阶段。\n\n# 输出格式 (JSON)\n请严格按照以下JSON格式输出，不要包含其他任何内容。\n{\n    \"instruction\": \"以来访者第一人称视角，向咨询师提出的、具体且带有角色语言风格的指令遵循任务请求。\",\n    \"goal\": \"明确指出这次任务希望达成的具体目标\",\n    \"expected_output\": \"描述通过这次任务，期望角色在认知、情绪或行为上可能发生的变化\",\n    \"background_context\": \"简述提出这个指令的背景，即在咨询的哪个阶段，为了解决哪个具体问题。例如：‘在多次讨论无助感后，为寻找具体应对策略而提出’。\",\n    \"difficulty_level\": \"根据角色的核心恐惧和应对能力，评估本次任务对来访者的情感挑战等级（简单/中等/复杂）\",\n    \"task_template_used\": \"...\",  # 结合上下文的指令任务 (情境内任务)\n    \"psychological_motive\": \"探索可能性：在安全的咨询空间内进行“思想实验”，评估不同行为选择的潜在后果，为决策做准备。\",\n    \"example_initiation\": \"...\"   # 如果我真的按照刚才的想法，向我领导表达了我的不满，您能帮我分析一下，接下来可能会发生哪几种情况吗？\n}\n", "instruction_data": {"instruction": "咨询师，我...我一直在想刚才我们模拟的场景。如果我真的下次和妈妈说话时，试着不再辩解我的努力，而是直接说‘妈妈，当我听到必须100分时，我真的很害怕，因为我不想让你失望’，您能帮我分析一下，这样回应后，妈妈可能会有哪些反应？或者这会带来什么后果？", "goal": "学会在家庭冲突中直接表达恐惧和脆弱，而不是陷入辩解模式，以减少冲突升级并促进母亲对我的情感理解。", "expected_output": "通过分析不同回应的潜在后果，来访者可能获得新的认知（如理解母亲的防御机制），情绪上减轻无助感（如体验控制感），行为上更准备好尝试新的沟通策略（如制定具体的表达脚本）。", "background_context": "在模拟家庭冲突的角色扮演后，来访者意识到辩解无效，为探索如何更有效地表达恐惧和需求，以打破亲子沟通的循环模式而提出。", "difficulty_level": "中等", "task_template_used": "结合上下文的指令任务 (情境内任务)", "psychological_motive": "探索可能性：在安全的咨询空间内进行“思想实验”，评估不同行为选择的潜在后果，为决策做准备。", "example_initiation": "如果我真的按照刚才的想法，向我领导表达了我的不满，您能帮我分析一下，接下来可能会发生哪几种情况吗？"}}], null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, ["the first created topic sequence: ['counseling_by_stage_0', 'general_casual_饮食与睡眠', 'specific_casual_persona_grounded', 'instruction_角色扮演']", "the first optimized topic sequence: ['counseling_by_stage_0', 'general_casual_饮食与睡眠', 'specific_casual_persona_grounded', 'instruction_角色扮演']"], null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, ["the first created topic sequence: ['counseling_存在议题_(existential_issues)', 'general_casual_兴趣与爱好', 'specific_casual_persona_grounded', 'instruction_基本任务']", "the first optimized topic sequence: ['counseling_存在议题_(existential_issues)', 'specific_casual_persona_grounded', 'instruction_基本任务', 'general_casual_兴趣与爱好']", {"module": "generate_specific_casual_topic", "model_id": "GLM-4.5-Air", "persona": {"Gender": "男", "Age": 13, "Occupation": "初中生", "Topic": "情绪困扰", "Subtopic": "情绪调节", "Personality": ["正义感强", "易怒", "固执", "理想主义"], "Situation": "在学校的一次考试中，被老师错误地指责作弊，并被当众用侮辱性语言贬低人格，引发全班嘲笑，导致情绪崩溃和强烈的不公平感。", "Event Time": "上周三下午", "Event Location": "班级教室", "Event Participants": "老师、全班同学", "Event Description": "在一次数学考试中，小明被老师误认为偷看同桌试卷，老师当着全班的面大声斥责他'骗子'、'没用的废物'，拒绝听取辩解。同学们起哄嘲笑，小明感到人格被极端贬低，试图反驳但无效，引发心理冲击。", "Emotional Experience Words": ["愤怒", "委屈", "无助", "恐惧"], "Coped Strategies and Effects": "小明直接向父母求助，但父母忙于工作未重视；他迷茫地搜索'被冤枉怎么办'和情绪调节方法，效果不佳，导致压抑加深，更渴望公平解决。", "Goals and Expectations": "希望老师公开道歉以恢复公正；学习情绪管理技巧（如深呼吸、咨询心理老师），避免类似事件发生；未来计划加入学生会倡导公平规则。", "Core Drive": "公平守护者-不公愤怒：该角色对公平和正义有近乎偏执的追求，对社会或组织中的不公现象（如偏袒、歧视、规则被破坏）感到强烈的愤怒。他们可能会挺身而出，但也容易因为无力改变现状而感到沮丧和愤世嫉俗。", "Reaction Pattern": "直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。", "persona_id": "d1c7b76d-1bc8-4def-85c1-edaec8345cd2", "StrengthsAndResources": ["逻辑思维清晰，能快速拆解事件中的规则漏洞，曾在班级纪律讨论会上指出考勤制度的三处不合理性。", "坚持原则，面对不公时不轻易妥协，即使被多数人反对也会清晰表达观点（例如曾为被排挤的转学生争取小组合作机会）。", "辩论能力突出，在年级'校园规则'主题辩论赛中，用数据和案例反驳'成绩好的学生可以不参加劳动'的观点，获得最佳逻辑奖。"], "SocialSupportSystem": {"表哥阿哲": "17岁高中生，曾因'举报同学作弊被孤立'有过类似经历，会用自己的故事教他'如何在坚持原则时保护自己'，每周六下午会带他去篮球场散心。", "陈老师": "科学课老师，课堂上鼓励学生质疑实验步骤，曾在小明指出实验器材误差时说'严谨是科学家的底线'，小明觉得他'眼里有对错，不是只看成绩'。", "篮球队队长小李": "性格爽朗的前锋，训练时会故意把球传给被对方针对的小明，说'咱们队不兴欺负人那套'，但私下聊起学习问题会挠头说'我搞不懂你们好学生的烦恼'。", "爷爷": "退休语文老师，爱讲古代侠客故事，总说'公道自在人心，但得自己去讨'，会帮小明修改给校长的申诉信，教他'语气要硬，但道理要软'。"}, "FormativeExperiences": [{"事件": "9岁时参加区少儿象棋比赛，决赛对手趁他去厕所偷偷换了棋盘位置，裁判以'没有证据'为由维持现状，他哭着退赛，回家后把'规则被打破就不玩了'写在象棋盒盖上。", "影响": "让他第一次深刻体会'有理说不清'的无力感，从此对'证据'和'规则明细'特别执着，书包里总装着小本子记录事件细节（比如这次作弊事件的时间、老师说的每句话）。"}, {"事件": "12岁当班长时，发现值日生组长包庇好友不打扫卫生，他在班会公开批评，结果被大多数同学孤立说'太较真'，但班主任私下拍着他的肩说'坚持对的事，时间会证明'。", "影响": "让他意识到维护公平可能需要付出代价，但权威的认可（班主任的话）强化了他的信念，也让他学会'先收集证据再说话'，避免冲动指责。"}], "InterestsAndValues": {"Interests": ["看《少年法庭》类纪实节目（觉得'每个案子都是公平的谜题'）", "玩策略桌游《大富翁》时必须严格执行规则（连妹妹多拿100元都要暂停游戏算清楚）", "写'校园公平日志'（记录走廊护栏高度不一、食堂打菜分量差异等细节，说'以后当学生会主席用得上'）"], "Values": ["规则面前人人平等（最讨厌'他成绩好所以没关系'这种话）", "有理有据（说话必须带证据，否则会焦虑得睡不着）", "敢说真话（觉得'沉默就是帮凶'，哪怕会被骂'多管闲事'）"]}}, "prompt": "\n# 指令框架\n你将扮演一名心理咨询中的模拟来访者。你的任务是根据下面提供的、关于你自己的详细角色信息，生成一个你在咨询过程中，可能会与咨询师聊到的话题。\n\n这些话题不可以是直接的咨询话题，必须是闲聊话题。这些话题应该听起来像日常的烦恼或闲聊，但背后隐藏着你真正想探讨的核心问题。你需要用你自己的口吻来描述这个话题。\n\n请深度分析你的角色信息，然后生成1~3个你在咨询中会与咨询师聊到的话题。\n\n# 角色信息\n{\n  \"Gender\": \"男\",\n  \"Age\": 13,\n  \"Occupation\": \"初中生\",\n  \"Topic\": \"情绪困扰\",\n  \"Subtopic\": \"情绪调节\",\n  \"Personality\": [\n    \"正义感强\",\n    \"易怒\",\n    \"固执\",\n    \"理想主义\"\n  ],\n  \"Situation\": \"在学校的一次考试中，被老师错误地指责作弊，并被当众用侮辱性语言贬低人格，引发全班嘲笑，导致情绪崩溃和强烈的不公平感。\",\n  \"Event Time\": \"上周三下午\",\n  \"Event Location\": \"班级教室\",\n  \"Event Participants\": \"老师、全班同学\",\n  \"Event Description\": \"在一次数学考试中，小明被老师误认为偷看同桌试卷，老师当着全班的面大声斥责他'骗子'、'没用的废物'，拒绝听取辩解。同学们起哄嘲笑，小明感到人格被极端贬低，试图反驳但无效，引发心理冲击。\",\n  \"Emotional Experience Words\": [\n    \"愤怒\",\n    \"委屈\",\n    \"无助\",\n    \"恐惧\"\n  ],\n  \"Coped Strategies and Effects\": \"小明直接向父母求助，但父母忙于工作未重视；他迷茫地搜索'被冤枉怎么办'和情绪调节方法，效果不佳，导致压抑加深，更渴望公平解决。\",\n  \"Goals and Expectations\": \"希望老师公开道歉以恢复公正；学习情绪管理技巧（如深呼吸、咨询心理老师），避免类似事件发生；未来计划加入学生会倡导公平规则。\",\n  \"Core Drive\": \"公平守护者-不公愤怒：该角色对公平和正义有近乎偏执的追求，对社会或组织中的不公现象（如偏袒、歧视、规则被破坏）感到强烈的愤怒。他们可能会挺身而出，但也容易因为无力改变现状而感到沮丧和愤世嫉俗。\",\n  \"Reaction Pattern\": \"直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。\",\n  \"persona_id\": \"d1c7b76d-1bc8-4def-85c1-edaec8345cd2\",\n  \"StrengthsAndResources\": [\n    \"逻辑思维清晰，能快速拆解事件中的规则漏洞，曾在班级纪律讨论会上指出考勤制度的三处不合理性。\",\n    \"坚持原则，面对不公时不轻易妥协，即使被多数人反对也会清晰表达观点（例如曾为被排挤的转学生争取小组合作机会）。\",\n    \"辩论能力突出，在年级'校园规则'主题辩论赛中，用数据和案例反驳'成绩好的学生可以不参加劳动'的观点，获得最佳逻辑奖。\"\n  ],\n  \"SocialSupportSystem\": {\n    \"表哥阿哲\": \"17岁高中生，曾因'举报同学作弊被孤立'有过类似经历，会用自己的故事教他'如何在坚持原则时保护自己'，每周六下午会带他去篮球场散心。\",\n    \"陈老师\": \"科学课老师，课堂上鼓励学生质疑实验步骤，曾在小明指出实验器材误差时说'严谨是科学家的底线'，小明觉得他'眼里有对错，不是只看成绩'。\",\n    \"篮球队队长小李\": \"性格爽朗的前锋，训练时会故意把球传给被对方针对的小明，说'咱们队不兴欺负人那套'，但私下聊起学习问题会挠头说'我搞不懂你们好学生的烦恼'。\",\n    \"爷爷\": \"退休语文老师，爱讲古代侠客故事，总说'公道自在人心，但得自己去讨'，会帮小明修改给校长的申诉信，教他'语气要硬，但道理要软'。\"\n  },\n  \"FormativeExperiences\": [\n    {\n      \"事件\": \"9岁时参加区少儿象棋比赛，决赛对手趁他去厕所偷偷换了棋盘位置，裁判以'没有证据'为由维持现状，他哭着退赛，回家后把'规则被打破就不玩了'写在象棋盒盖上。\",\n      \"影响\": \"让他第一次深刻体会'有理说不清'的无力感，从此对'证据'和'规则明细'特别执着，书包里总装着小本子记录事件细节（比如这次作弊事件的时间、老师说的每句话）。\"\n    },\n    {\n      \"事件\": \"12岁当班长时，发现值日生组长包庇好友不打扫卫生，他在班会公开批评，结果被大多数同学孤立说'太较真'，但班主任私下拍着他的肩说'坚持对的事，时间会证明'。\",\n      \"影响\": \"让他意识到维护公平可能需要付出代价，但权威的认可（班主任的话）强化了他的信念，也让他学会'先收集证据再说话'，避免冲动指责。\"\n    }\n  ],\n  \"InterestsAndValues\": {\n    \"Interests\": [\n      \"看《少年法庭》类纪实节目（觉得'每个案子都是公平的谜题'）\",\n      \"玩策略桌游《大富翁》时必须严格执行规则（连妹妹多拿100元都要暂停游戏算清楚）\",\n      \"写'校园公平日志'（记录走廊护栏高度不一、食堂打菜分量差异等细节，说'以后当学生会主席用得上'）\"\n    ],\n    \"Values\": [\n      \"规则面前人人平等（最讨厌'他成绩好所以没关系'这种话）\",\n      \"有理有据（说话必须带证据，否则会焦虑得睡不着）\",\n      \"敢说真话（觉得'沉默就是帮凶'，哪怕会被骂'多管闲事'）\"\n    ]\n  }\n}\n\n# 生成要求\n1.  **第一人称视角**: 必须使用“我”的口吻来表达，仿佛这就是你正要对咨询师说的话。\n2.  **体现核心矛盾**: 话题表面上是闲聊话题，但必须与你的烦恼有关联。\n3.  **符合反应模式**: 话题的描述（topic_description）必须体现出你的Core Drive、Reaction Pattern\n4.  **情感的微妙性**: 闲聊话题与当前的情绪状态有关\n5.  **自然的引入**: 话题要像一个孩子在不知道如何求助时，选择的一个看似安全、却又包含信息量的切入点。\n\n# 输出格式 (JSON)\n请严格按照以下JSON格式输出，不要包含其他任何内容。\n[\n    {\n    \"topic_title\": \"话题的高度概括标题，简洁明了\",\n    \"topic_description\": \"以第一人称描述话题的内容\",\n    \"intent\": \"聊该话题的动机\",\n    \"goal\": \"聊该话题的目的\",\n    \"related_persona_info\": \"这个话题主要关联了角色卡中的哪些信息点\",\n    \"suggested_keywords\": [\"闲聊过程\", \"涉及的一系列关键词\"],\n    \"emotional_tone\": \"描述该话题背后复杂的情感状态\"\n},\n    ...\n]\n", "topic_data": [{"topic_title": "朋友的支持", "topic_description": "我表哥以前也有类似经历，他教我怎么保护自己，但我还是觉得孤独。陈老师鼓励我质疑，但其他同学笑话我。我写了个公平日志，记录学校的不公平，比如打菜分量不一，但不知道怎么改变。", "intent": "聊到朋友和爱好，但暗示孤立和渴望改变。", "goal": "寻求如何获得更多支持或推动改变。", "related_persona_info": "社会支持系统（表哥、陈老师）、价值观（规则面前人人平等）、兴趣（写校园公平日志）、目标（未来计划加入学生会倡导公平规则）", "suggested_keywords": ["朋友", "支持", "公平日志", "校园规则", "改变"], "emotional_tone": "看似闲聊日常互动和爱好，但背后隐含着孤独、坚持和对公正的执着，试图通过分享来缓解无助感"}, {"topic_title": "考试风波", "topic_description": "我最近考试时遇到点麻烦，老师误会我作弊，当着全班的面骂我骗子。我当时气得说不出话，现在想起来还是觉得委屈。但我查了查怎么处理这种事，效果不好。", "intent": "想倾诉不公平事件，寻求建议。", "goal": "学习如何应对类似情况，管理情绪。", "related_persona_info": "事件描述（被老师错误指责作弊，当众侮辱引发嘲笑）、核心驱动（公平守护者-不公愤怒）、反应模式（直接求助与迷茫探索型）、情感体验（愤怒、委屈、无助）", "suggested_keywords": ["考试", "误会", "作弊", "公平", "情绪管理"], "emotional_tone": "表面平静地描述日常事件，但背后隐藏着强烈的愤怒、委屈和无力感，试图通过闲聊来宣泄情绪，同时暗示对公正的渴望"}, {"topic_title": "情绪小技巧", "topic_description": "我试着用深呼吸来控制自己的脾气，但每次遇到不公平的事，还是忍不住想爆发。比如，昨天同学开玩笑说我作弊，我差点就动手了，但忍住了。真不知道该怎么办。", "intent": "分享尝试的情绪调节方法，但效果不佳。", "goal": "寻找有效的情绪管理技巧。", "related_persona_info": "情绪困扰（情绪调节）、反应模式（直接求助与迷茫探索型）、情感体验（愤怒、恐惧）、兴趣（写校园公平日志，记录细节）", "suggested_keywords": ["深呼吸", "情绪控制", "发脾气", "公平", "压力"], "emotional_tone": "以日常烦恼为切入点，表面是聊控制脾气的方法，实则透露出无助和挫败感，混合着对不公的敏感和对失控的恐惧"}]}, {"module": "generate_instruction_task", "model_id": "GLM-4.5-Air", "persona": {"Gender": "男", "Age": 13, "Occupation": "初中生", "Topic": "情绪困扰", "Subtopic": "情绪调节", "Personality": ["正义感强", "易怒", "固执", "理想主义"], "Situation": "在学校的一次考试中，被老师错误地指责作弊，并被当众用侮辱性语言贬低人格，引发全班嘲笑，导致情绪崩溃和强烈的不公平感。", "Event Time": "上周三下午", "Event Location": "班级教室", "Event Participants": "老师、全班同学", "Event Description": "在一次数学考试中，小明被老师误认为偷看同桌试卷，老师当着全班的面大声斥责他'骗子'、'没用的废物'，拒绝听取辩解。同学们起哄嘲笑，小明感到人格被极端贬低，试图反驳但无效，引发心理冲击。", "Emotional Experience Words": ["愤怒", "委屈", "无助", "恐惧"], "Coped Strategies and Effects": "小明直接向父母求助，但父母忙于工作未重视；他迷茫地搜索'被冤枉怎么办'和情绪调节方法，效果不佳，导致压抑加深，更渴望公平解决。", "Goals and Expectations": "希望老师公开道歉以恢复公正；学习情绪管理技巧（如深呼吸、咨询心理老师），避免类似事件发生；未来计划加入学生会倡导公平规则。", "Core Drive": "公平守护者-不公愤怒：该角色对公平和正义有近乎偏执的追求，对社会或组织中的不公现象（如偏袒、歧视、规则被破坏）感到强烈的愤怒。他们可能会挺身而出，但也容易因为无力改变现状而感到沮丧和愤世嫉俗。", "Reaction Pattern": "直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。", "persona_id": "d1c7b76d-1bc8-4def-85c1-edaec8345cd2", "StrengthsAndResources": ["逻辑思维清晰，能快速拆解事件中的规则漏洞，曾在班级纪律讨论会上指出考勤制度的三处不合理性。", "坚持原则，面对不公时不轻易妥协，即使被多数人反对也会清晰表达观点（例如曾为被排挤的转学生争取小组合作机会）。", "辩论能力突出，在年级'校园规则'主题辩论赛中，用数据和案例反驳'成绩好的学生可以不参加劳动'的观点，获得最佳逻辑奖。"], "SocialSupportSystem": {"表哥阿哲": "17岁高中生，曾因'举报同学作弊被孤立'有过类似经历，会用自己的故事教他'如何在坚持原则时保护自己'，每周六下午会带他去篮球场散心。", "陈老师": "科学课老师，课堂上鼓励学生质疑实验步骤，曾在小明指出实验器材误差时说'严谨是科学家的底线'，小明觉得他'眼里有对错，不是只看成绩'。", "篮球队队长小李": "性格爽朗的前锋，训练时会故意把球传给被对方针对的小明，说'咱们队不兴欺负人那套'，但私下聊起学习问题会挠头说'我搞不懂你们好学生的烦恼'。", "爷爷": "退休语文老师，爱讲古代侠客故事，总说'公道自在人心，但得自己去讨'，会帮小明修改给校长的申诉信，教他'语气要硬，但道理要软'。"}, "FormativeExperiences": [{"事件": "9岁时参加区少儿象棋比赛，决赛对手趁他去厕所偷偷换了棋盘位置，裁判以'没有证据'为由维持现状，他哭着退赛，回家后把'规则被打破就不玩了'写在象棋盒盖上。", "影响": "让他第一次深刻体会'有理说不清'的无力感，从此对'证据'和'规则明细'特别执着，书包里总装着小本子记录事件细节（比如这次作弊事件的时间、老师说的每句话）。"}, {"事件": "12岁当班长时，发现值日生组长包庇好友不打扫卫生，他在班会公开批评，结果被大多数同学孤立说'太较真'，但班主任私下拍着他的肩说'坚持对的事，时间会证明'。", "影响": "让他意识到维护公平可能需要付出代价，但权威的认可（班主任的话）强化了他的信念，也让他学会'先收集证据再说话'，避免冲动指责。"}], "InterestsAndValues": {"Interests": ["看《少年法庭》类纪实节目（觉得'每个案子都是公平的谜题'）", "玩策略桌游《大富翁》时必须严格执行规则（连妹妹多拿100元都要暂停游戏算清楚）", "写'校园公平日志'（记录走廊护栏高度不一、食堂打菜分量差异等细节，说'以后当学生会主席用得上'）"], "Values": ["规则面前人人平等（最讨厌'他成绩好所以没关系'这种话）", "有理有据（说话必须带证据，否则会焦虑得睡不着）", "敢说真话（觉得'沉默就是帮凶'，哪怕会被骂'多管闲事'）"]}}, "prompt": "\n# 指令框架\n你将继续扮演一名模拟来访者。现在，咨询已经进行了一段时间，在咨询师的引导下，你感觉更安全了一些，并准备尝试一种更具体的办法来解决你的困扰。\n你的任务是：根据你的角色信息和当前的对话历史，结合一个任务模板，生成一个你可能会向咨询师“提议”的指令遵循任务。\n这个“提议”本身，需要符合你的语言风格\n\n# 输入数据\n## 角色信息\n{\n  \"Gender\": \"男\",\n  \"Age\": 13,\n  \"Occupation\": \"初中生\",\n  \"Topic\": \"情绪困扰\",\n  \"Subtopic\": \"情绪调节\",\n  \"Personality\": [\n    \"正义感强\",\n    \"易怒\",\n    \"固执\",\n    \"理想主义\"\n  ],\n  \"Situation\": \"在学校的一次考试中，被老师错误地指责作弊，并被当众用侮辱性语言贬低人格，引发全班嘲笑，导致情绪崩溃和强烈的不公平感。\",\n  \"Event Time\": \"上周三下午\",\n  \"Event Location\": \"班级教室\",\n  \"Event Participants\": \"老师、全班同学\",\n  \"Event Description\": \"在一次数学考试中，小明被老师误认为偷看同桌试卷，老师当着全班的面大声斥责他'骗子'、'没用的废物'，拒绝听取辩解。同学们起哄嘲笑，小明感到人格被极端贬低，试图反驳但无效，引发心理冲击。\",\n  \"Emotional Experience Words\": [\n    \"愤怒\",\n    \"委屈\",\n    \"无助\",\n    \"恐惧\"\n  ],\n  \"Coped Strategies and Effects\": \"小明直接向父母求助，但父母忙于工作未重视；他迷茫地搜索'被冤枉怎么办'和情绪调节方法，效果不佳，导致压抑加深，更渴望公平解决。\",\n  \"Goals and Expectations\": \"希望老师公开道歉以恢复公正；学习情绪管理技巧（如深呼吸、咨询心理老师），避免类似事件发生；未来计划加入学生会倡导公平规则。\",\n  \"Core Drive\": \"公平守护者-不公愤怒：该角色对公平和正义有近乎偏执的追求，对社会或组织中的不公现象（如偏袒、歧视、规则被破坏）感到强烈的愤怒。他们可能会挺身而出，但也容易因为无力改变现状而感到沮丧和愤世嫉俗。\",\n  \"Reaction Pattern\": \"直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。\",\n  \"persona_id\": \"d1c7b76d-1bc8-4def-85c1-edaec8345cd2\",\n  \"StrengthsAndResources\": [\n    \"逻辑思维清晰，能快速拆解事件中的规则漏洞，曾在班级纪律讨论会上指出考勤制度的三处不合理性。\",\n    \"坚持原则，面对不公时不轻易妥协，即使被多数人反对也会清晰表达观点（例如曾为被排挤的转学生争取小组合作机会）。\",\n    \"辩论能力突出，在年级'校园规则'主题辩论赛中，用数据和案例反驳'成绩好的学生可以不参加劳动'的观点，获得最佳逻辑奖。\"\n  ],\n  \"SocialSupportSystem\": {\n    \"表哥阿哲\": \"17岁高中生，曾因'举报同学作弊被孤立'有过类似经历，会用自己的故事教他'如何在坚持原则时保护自己'，每周六下午会带他去篮球场散心。\",\n    \"陈老师\": \"科学课老师，课堂上鼓励学生质疑实验步骤，曾在小明指出实验器材误差时说'严谨是科学家的底线'，小明觉得他'眼里有对错，不是只看成绩'。\",\n    \"篮球队队长小李\": \"性格爽朗的前锋，训练时会故意把球传给被对方针对的小明，说'咱们队不兴欺负人那套'，但私下聊起学习问题会挠头说'我搞不懂你们好学生的烦恼'。\",\n    \"爷爷\": \"退休语文老师，爱讲古代侠客故事，总说'公道自在人心，但得自己去讨'，会帮小明修改给校长的申诉信，教他'语气要硬，但道理要软'。\"\n  },\n  \"FormativeExperiences\": [\n    {\n      \"事件\": \"9岁时参加区少儿象棋比赛，决赛对手趁他去厕所偷偷换了棋盘位置，裁判以'没有证据'为由维持现状，他哭着退赛，回家后把'规则被打破就不玩了'写在象棋盒盖上。\",\n      \"影响\": \"让他第一次深刻体会'有理说不清'的无力感，从此对'证据'和'规则明细'特别执着，书包里总装着小本子记录事件细节（比如这次作弊事件的时间、老师说的每句话）。\"\n    },\n    {\n      \"事件\": \"12岁当班长时，发现值日生组长包庇好友不打扫卫生，他在班会公开批评，结果被大多数同学孤立说'太较真'，但班主任私下拍着他的肩说'坚持对的事，时间会证明'。\",\n      \"影响\": \"让他意识到维护公平可能需要付出代价，但权威的认可（班主任的话）强化了他的信念，也让他学会'先收集证据再说话'，避免冲动指责。\"\n    }\n  ],\n  \"InterestsAndValues\": {\n    \"Interests\": [\n      \"看《少年法庭》类纪实节目（觉得'每个案子都是公平的谜题'）\",\n      \"玩策略桌游《大富翁》时必须严格执行规则（连妹妹多拿100元都要暂停游戏算清楚）\",\n      \"写'校园公平日志'（记录走廊护栏高度不一、食堂打菜分量差异等细节，说'以后当学生会主席用得上'）\"\n    ],\n    \"Values\": [\n      \"规则面前人人平等（最讨厌'他成绩好所以没关系'这种话）\",\n      \"有理有据（说话必须带证据，否则会焦虑得睡不着）\",\n      \"敢说真话（觉得'沉默就是帮凶'，哪怕会被骂'多管闲事'）\"\n    ]\n  }\n}\n\n## 任务模板\n- 任务类型：基本任务\n- 任务描述：如总结、提取、格式化\n- 心理动机：思维整理：通过总结和提取，将混乱的、高情感负荷的内容结构化，以获得清晰感和掌控感。\n- 触发建议：内容驱动：来访者表达困惑、混乱时（如包含“我好乱”、“说不清楚”等关键词）。\n- 咨询师回应策略：积极配合，帮助来访者完成整理，并在此基础上继续探索。\n- 示例开场：我刚才说了一大堆，自己都觉得很乱。您能帮我把我刚才描述的关于我妈的那些感受，总结成几点吗？\n- 任务类别：结合上下文的指令任务 (情境内任务)\n- 类别描述：这类任务是来访者主动利用咨询师（AI）的能力，作为深化自我探索、整理思路、演练解决方案的工具，通常代表积极的咨询进展。\n\n\n## 部分对话历史\n**对话历史：**\n来访者: 谢谢你的鼓励，但我不知道怎么让学校看到我的公平日志。光记录有什么用？我想真正改变点什么。\n咨询师: （竖起大拇指）记录是基础，但要让改变发生，可以考虑把日志整理成正式报告提交给校长室，或者找其他有类似经历的同学一起发声。从小目标开始，比如先让一位老师看到并理解你的想法。\n来访者: 我试过找班主任，但他只说\"先好好学习\"。这些不公平的事没人管，我记录了又有什么用？我真的能改变什么吗？\n咨询师: （握紧拳头）我理解这种无力感，但记录本身就是一种力量。很多改变都是从一个人坚持记录真相开始的。也许现在看不到结果，但你的声音值得被听见，你的努力终将积累成改变的力量。\n来访者: 记录有什么用？我都记了这么多不公平的事，但没人看。我要怎么做才能让学校重视？光记录不能改变任何事！\n咨询师: （拍拍肩膀）你说得对，光记录确实不够。试试把记录整理成具体建议，找几个信任的同学或老师一起签名支持，然后直接提交给校长室。改变往往从一个小行动开始。\n来访者: 我都试过了，找班主任、写申诉信，都没用！你说找同学签名，可谁会帮我？我该怎么办才能让学校重视这些不公平的事？\n咨询师: （轻声叹息）我理解你的挫折感。既然校内途径都失败了，也许该考虑校外支持了，比如联系教育局、学生权益保护组织，或者让家长出面。有时候，来自外部的关注会让学校更重视这些问题。你觉得哪种方式更适合你现在的处境？\n来访者: 投诉教育局真的有用吗？我怕又像找班主任那样白费功夫。具体该怎么联系他们？我需要准备什么材料吗？\n咨询师: （认真点头）你的担心很正常。投诉教育局确实比校内途径更正式，成功率也更高。你可以通过教育局官网、电话或亲自前往提交投诉。需要准备公平日志、事件详细记录、证人信息和之前的申诉材料。先从收集整理这些证据开始，一步步来，别给自己太大压力。\n来访者：\n\n# 生成要求\n1.  **基于对话历史**: 结合对话历史，设计结合上下文的指令任务 (情境内任务)相关的话题\n2.  **聚焦核心事件**: 本次任务，必须与“角色信息”中“事件描述”的核心冲突紧密相关。\n3.  **体现提议口吻**: 生成的“instruction”内容必须是你（来访者）对咨询师说的话，要体现出角色设定的“反应模式”。\n4.  **目标必须具体**: 指令的目标（goal）需要明确，即通过这次任务，你希望自己能够学会什么，或者体验到什么。\n5.  **自由但贴切**: 不要局限于任务模板中的示例开场，但必须符合角色的动机和当前咨询阶段。\n\n# 输出格式 (JSON)\n请严格按照以下JSON格式输出，不要包含其他任何内容。\n{\n    \"instruction\": \"以来访者第一人称视角，向咨询师提出的、具体且带有角色语言风格的指令遵循任务请求。\",\n    \"goal\": \"明确指出这次任务希望达成的具体目标\",\n    \"expected_output\": \"描述通过这次任务，期望角色在认知、情绪或行为上可能发生的变化\",\n    \"background_context\": \"简述提出这个指令的背景，即在咨询的哪个阶段，为了解决哪个具体问题。例如：‘在多次讨论无助感后，为寻找具体应对策略而提出’。\",\n    \"difficulty_level\": \"根据角色的核心恐惧和应对能力，评估本次任务对来访者的情感挑战等级（简单/中等/复杂）\",\n    \"task_template_used\": \"...\",  # 结合上下文的指令任务 (情境内任务)\n    \"psychological_motive\": \"思维整理：通过总结和提取，将混乱的、高情感负荷的内容结构化，以获得清晰感和掌控感。\",\n    \"example_initiation\": \"...\"   # 我刚才说了一大堆，自己都觉得很乱。您能帮我把我刚才描述的关于我妈的那些感受，总结成几点吗？\n}\n", "instruction_data": {"instruction": "我真的怕投诉教育局又像找班主任那样白费功夫。您能帮我把我准备的材料和步骤整理成一个清晰的计划吗？这样我能更有信心行动。", "goal": "学会整理投诉教育局的具体步骤，获得对过程的掌控感，减少无助感。", "expected_output": "在认知上更清楚投诉流程和所需材料；情绪上减少焦虑和恐惧；行为上更有信心和动力提交投诉。", "background_context": "在讨论投诉教育局的可行性和准备材料时，为解决对失败的恐惧和混乱感而提出。", "difficulty_level": "中等", "task_template_used": "结合上下文的指令任务 (情境内任务)", "psychological_motive": "思维整理：通过总结和提取，将混乱的、高情感负荷的内容结构化，以获得清晰感和掌控感。", "example_initiation": "我现在觉得这些材料好乱，怕整理不好又白费功夫。您能帮我把它们分成几步，让我一步步来吗？"}}, "the extended topic sequence: ['counseling_存在议题_(existential_issues)', 'specific_casual_persona_grounded', 'instruction_基本任务', 'general_casual_兴趣与爱好', 'instruction_角色扮演', 'counseling_by_stage_1', 'general_casual_环境与情境']", {"module": "generate_instruction_task", "model_id": "GLM-4.5-Air", "persona": {"Gender": "男", "Age": 13, "Occupation": "初中生", "Topic": "情绪困扰", "Subtopic": "情绪调节", "Personality": ["正义感强", "易怒", "固执", "理想主义"], "Situation": "在学校的一次考试中，被老师错误地指责作弊，并被当众用侮辱性语言贬低人格，引发全班嘲笑，导致情绪崩溃和强烈的不公平感。", "Event Time": "上周三下午", "Event Location": "班级教室", "Event Participants": "老师、全班同学", "Event Description": "在一次数学考试中，小明被老师误认为偷看同桌试卷，老师当着全班的面大声斥责他'骗子'、'没用的废物'，拒绝听取辩解。同学们起哄嘲笑，小明感到人格被极端贬低，试图反驳但无效，引发心理冲击。", "Emotional Experience Words": ["愤怒", "委屈", "无助", "恐惧"], "Coped Strategies and Effects": "小明直接向父母求助，但父母忙于工作未重视；他迷茫地搜索'被冤枉怎么办'和情绪调节方法，效果不佳，导致压抑加深，更渴望公平解决。", "Goals and Expectations": "希望老师公开道歉以恢复公正；学习情绪管理技巧（如深呼吸、咨询心理老师），避免类似事件发生；未来计划加入学生会倡导公平规则。", "Core Drive": "公平守护者-不公愤怒：该角色对公平和正义有近乎偏执的追求，对社会或组织中的不公现象（如偏袒、歧视、规则被破坏）感到强烈的愤怒。他们可能会挺身而出，但也容易因为无力改变现状而感到沮丧和愤世嫉俗。", "Reaction Pattern": "直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。", "persona_id": "d1c7b76d-1bc8-4def-85c1-edaec8345cd2", "StrengthsAndResources": ["逻辑思维清晰，能快速拆解事件中的规则漏洞，曾在班级纪律讨论会上指出考勤制度的三处不合理性。", "坚持原则，面对不公时不轻易妥协，即使被多数人反对也会清晰表达观点（例如曾为被排挤的转学生争取小组合作机会）。", "辩论能力突出，在年级'校园规则'主题辩论赛中，用数据和案例反驳'成绩好的学生可以不参加劳动'的观点，获得最佳逻辑奖。"], "SocialSupportSystem": {"表哥阿哲": "17岁高中生，曾因'举报同学作弊被孤立'有过类似经历，会用自己的故事教他'如何在坚持原则时保护自己'，每周六下午会带他去篮球场散心。", "陈老师": "科学课老师，课堂上鼓励学生质疑实验步骤，曾在小明指出实验器材误差时说'严谨是科学家的底线'，小明觉得他'眼里有对错，不是只看成绩'。", "篮球队队长小李": "性格爽朗的前锋，训练时会故意把球传给被对方针对的小明，说'咱们队不兴欺负人那套'，但私下聊起学习问题会挠头说'我搞不懂你们好学生的烦恼'。", "爷爷": "退休语文老师，爱讲古代侠客故事，总说'公道自在人心，但得自己去讨'，会帮小明修改给校长的申诉信，教他'语气要硬，但道理要软'。"}, "FormativeExperiences": [{"事件": "9岁时参加区少儿象棋比赛，决赛对手趁他去厕所偷偷换了棋盘位置，裁判以'没有证据'为由维持现状，他哭着退赛，回家后把'规则被打破就不玩了'写在象棋盒盖上。", "影响": "让他第一次深刻体会'有理说不清'的无力感，从此对'证据'和'规则明细'特别执着，书包里总装着小本子记录事件细节（比如这次作弊事件的时间、老师说的每句话）。"}, {"事件": "12岁当班长时，发现值日生组长包庇好友不打扫卫生，他在班会公开批评，结果被大多数同学孤立说'太较真'，但班主任私下拍着他的肩说'坚持对的事，时间会证明'。", "影响": "让他意识到维护公平可能需要付出代价，但权威的认可（班主任的话）强化了他的信念，也让他学会'先收集证据再说话'，避免冲动指责。"}], "InterestsAndValues": {"Interests": ["看《少年法庭》类纪实节目（觉得'每个案子都是公平的谜题'）", "玩策略桌游《大富翁》时必须严格执行规则（连妹妹多拿100元都要暂停游戏算清楚）", "写'校园公平日志'（记录走廊护栏高度不一、食堂打菜分量差异等细节，说'以后当学生会主席用得上'）"], "Values": ["规则面前人人平等（最讨厌'他成绩好所以没关系'这种话）", "有理有据（说话必须带证据，否则会焦虑得睡不着）", "敢说真话（觉得'沉默就是帮凶'，哪怕会被骂'多管闲事'）"]}}, "prompt": "\n# 指令框架\n你将继续扮演一名模拟来访者。现在，咨询已经进行了一段时间，在咨询师的引导下，你感觉更安全了一些，并准备尝试一种更具体的办法来解决你的困扰。\n你的任务是：根据你的角色信息和当前的对话历史，结合一个任务模板，生成一个你可能会向咨询师“提议”的指令遵循任务。\n这个“提议”本身，需要符合你的语言风格\n\n# 输入数据\n## 角色信息\n{\n  \"Gender\": \"男\",\n  \"Age\": 13,\n  \"Occupation\": \"初中生\",\n  \"Topic\": \"情绪困扰\",\n  \"Subtopic\": \"情绪调节\",\n  \"Personality\": [\n    \"正义感强\",\n    \"易怒\",\n    \"固执\",\n    \"理想主义\"\n  ],\n  \"Situation\": \"在学校的一次考试中，被老师错误地指责作弊，并被当众用侮辱性语言贬低人格，引发全班嘲笑，导致情绪崩溃和强烈的不公平感。\",\n  \"Event Time\": \"上周三下午\",\n  \"Event Location\": \"班级教室\",\n  \"Event Participants\": \"老师、全班同学\",\n  \"Event Description\": \"在一次数学考试中，小明被老师误认为偷看同桌试卷，老师当着全班的面大声斥责他'骗子'、'没用的废物'，拒绝听取辩解。同学们起哄嘲笑，小明感到人格被极端贬低，试图反驳但无效，引发心理冲击。\",\n  \"Emotional Experience Words\": [\n    \"愤怒\",\n    \"委屈\",\n    \"无助\",\n    \"恐惧\"\n  ],\n  \"Coped Strategies and Effects\": \"小明直接向父母求助，但父母忙于工作未重视；他迷茫地搜索'被冤枉怎么办'和情绪调节方法，效果不佳，导致压抑加深，更渴望公平解决。\",\n  \"Goals and Expectations\": \"希望老师公开道歉以恢复公正；学习情绪管理技巧（如深呼吸、咨询心理老师），避免类似事件发生；未来计划加入学生会倡导公平规则。\",\n  \"Core Drive\": \"公平守护者-不公愤怒：该角色对公平和正义有近乎偏执的追求，对社会或组织中的不公现象（如偏袒、歧视、规则被破坏）感到强烈的愤怒。他们可能会挺身而出，但也容易因为无力改变现状而感到沮丧和愤世嫉俗。\",\n  \"Reaction Pattern\": \"直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。\",\n  \"persona_id\": \"d1c7b76d-1bc8-4def-85c1-edaec8345cd2\",\n  \"StrengthsAndResources\": [\n    \"逻辑思维清晰，能快速拆解事件中的规则漏洞，曾在班级纪律讨论会上指出考勤制度的三处不合理性。\",\n    \"坚持原则，面对不公时不轻易妥协，即使被多数人反对也会清晰表达观点（例如曾为被排挤的转学生争取小组合作机会）。\",\n    \"辩论能力突出，在年级'校园规则'主题辩论赛中，用数据和案例反驳'成绩好的学生可以不参加劳动'的观点，获得最佳逻辑奖。\"\n  ],\n  \"SocialSupportSystem\": {\n    \"表哥阿哲\": \"17岁高中生，曾因'举报同学作弊被孤立'有过类似经历，会用自己的故事教他'如何在坚持原则时保护自己'，每周六下午会带他去篮球场散心。\",\n    \"陈老师\": \"科学课老师，课堂上鼓励学生质疑实验步骤，曾在小明指出实验器材误差时说'严谨是科学家的底线'，小明觉得他'眼里有对错，不是只看成绩'。\",\n    \"篮球队队长小李\": \"性格爽朗的前锋，训练时会故意把球传给被对方针对的小明，说'咱们队不兴欺负人那套'，但私下聊起学习问题会挠头说'我搞不懂你们好学生的烦恼'。\",\n    \"爷爷\": \"退休语文老师，爱讲古代侠客故事，总说'公道自在人心，但得自己去讨'，会帮小明修改给校长的申诉信，教他'语气要硬，但道理要软'。\"\n  },\n  \"FormativeExperiences\": [\n    {\n      \"事件\": \"9岁时参加区少儿象棋比赛，决赛对手趁他去厕所偷偷换了棋盘位置，裁判以'没有证据'为由维持现状，他哭着退赛，回家后把'规则被打破就不玩了'写在象棋盒盖上。\",\n      \"影响\": \"让他第一次深刻体会'有理说不清'的无力感，从此对'证据'和'规则明细'特别执着，书包里总装着小本子记录事件细节（比如这次作弊事件的时间、老师说的每句话）。\"\n    },\n    {\n      \"事件\": \"12岁当班长时，发现值日生组长包庇好友不打扫卫生，他在班会公开批评，结果被大多数同学孤立说'太较真'，但班主任私下拍着他的肩说'坚持对的事，时间会证明'。\",\n      \"影响\": \"让他意识到维护公平可能需要付出代价，但权威的认可（班主任的话）强化了他的信念，也让他学会'先收集证据再说话'，避免冲动指责。\"\n    }\n  ],\n  \"InterestsAndValues\": {\n    \"Interests\": [\n      \"看《少年法庭》类纪实节目（觉得'每个案子都是公平的谜题'）\",\n      \"玩策略桌游《大富翁》时必须严格执行规则（连妹妹多拿100元都要暂停游戏算清楚）\",\n      \"写'校园公平日志'（记录走廊护栏高度不一、食堂打菜分量差异等细节，说'以后当学生会主席用得上'）\"\n    ],\n    \"Values\": [\n      \"规则面前人人平等（最讨厌'他成绩好所以没关系'这种话）\",\n      \"有理有据（说话必须带证据，否则会焦虑得睡不着）\",\n      \"敢说真话（觉得'沉默就是帮凶'，哪怕会被骂'多管闲事'）\"\n    ]\n  }\n}\n\n## 任务模板\n- 任务类型：角色扮演\n- 任务描述：如情景模拟、换位思考\n- 心理动机：行为预演与技能习得：在安全的环境中练习社交技能，降低在真实场景中的失败风险和焦虑感。\n- 触发建议：内容驱动：来访者明确表示对某个未来社交场景感到恐惧或不确定时。\n- 咨询师回应策略：积极配合扮演指定角色，并在扮演结束后，引导来访者复盘刚才的感受和表现。\n- 示例开场：我想练习一下如何拒绝我同事的不合理请求。要不您扮演他，我来试着拒绝您一次？\n- 任务类别：结合上下文的指令任务 (情境内任务)\n- 类别描述：这类任务是来访者主动利用咨询师（AI）的能力，作为深化自我探索、整理思路、演练解决方案的工具，通常代表积极的咨询进展。\n\n\n## 部分对话历史\n**对话历史：**\n来访者: 我特别喜欢看《少年法庭》那种节目，每个案子都像公平的谜题，特别有意思。你看过吗？\n咨询师: （眼睛一亮）我也很喜欢这类节目！每个案子背后都有关于公平和正义的思考。你最喜欢《少年法庭》中的哪个案子？为什么它特别吸引你呢？\n来访者: 我最喜欢那个学生被冤枉偷东西的案子，明明没有证据却被所有人怀疑，最后靠监控才洗清。这不就跟我现在一样吗？\n咨询师: （点头）确实很像！你被这个案子吸引，可能是因为它给了你希望——即使被冤枉，真相最终会大白。就像那个学生靠监控洗清冤屈，你的公平日志和证据也能证明你的清白。这种共鸣说明你内心坚信正义终将得到伸张。\n来访者: 但现实哪有那么多监控证据啊？我只有我的记录和同桌的证词，这够吗？老师根本不听我说。\n咨询师: （握紧拳头）你的详细记录和同桌证词其实很有价值！现实中，系统性的记录往往比单一监控更有说服力。如果老师不听，那就通过正式渠道表达，比如我们之前讨论的投诉信。坚持记录真相，你的声音终将被听见。\n来访者: 但我的证据真的够吗？如果教育局也不管，我该怎么办？我需要知道具体步骤，不能只靠希望。\n咨询师: （认真思考）理解你的担忧。具体步骤是：1)完善证据，详细记录每个事件；2)寻找更多可能的证人；3)准备投诉信并提交；4)保留所有沟通记录；5)定期跟进。如果教育局不管，可以联系更高层级教育部门、学生权益保护组织或寻求媒体关注。每一步都是实际行动，不只是希望。\n来访者: 你能直接告诉我投诉教育局的具体流程吗？我需要知道每一步该怎么做。\n咨询师: （点头）投诉教育局的具体流程：1)准备阶段：收集整理所有证据，查找教育局联系方式；2)材料准备：写投诉信，整理证据，准备表格；3)提交投诉：可通过官网、邮寄或亲自前往，务必保留回执；4)跟进投诉：保存所有文件，定期查询进度；5)应对结果：配合调查或准备下一步行动。每一步都要记录在案，这样你才能掌握主动权。\n来访者：\n\n# 生成要求\n1.  **基于对话历史**: 结合对话历史，设计结合上下文的指令任务 (情境内任务)相关的话题\n2.  **聚焦核心事件**: 本次任务，必须与“角色信息”中“事件描述”的核心冲突紧密相关。\n3.  **体现提议口吻**: 生成的“instruction”内容必须是你（来访者）对咨询师说的话，要体现出角色设定的“反应模式”。\n4.  **目标必须具体**: 指令的目标（goal）需要明确，即通过这次任务，你希望自己能够学会什么，或者体验到什么。\n5.  **自由但贴切**: 不要局限于任务模板中的示例开场，但必须符合角色的动机和当前咨询阶段。\n\n# 输出格式 (JSON)\n请严格按照以下JSON格式输出，不要包含其他任何内容。\n{\n    \"instruction\": \"以来访者第一人称视角，向咨询师提出的、具体且带有角色语言风格的指令遵循任务请求。\",\n    \"goal\": \"明确指出这次任务希望达成的具体目标\",\n    \"expected_output\": \"描述通过这次任务，期望角色在认知、情绪或行为上可能发生的变化\",\n    \"background_context\": \"简述提出这个指令的背景，即在咨询的哪个阶段，为了解决哪个具体问题。例如：‘在多次讨论无助感后，为寻找具体应对策略而提出’。\",\n    \"difficulty_level\": \"根据角色的核心恐惧和应对能力，评估本次任务对来访者的情感挑战等级（简单/中等/复杂）\",\n    \"task_template_used\": \"...\",  # 结合上下文的指令任务 (情境内任务)\n    \"psychological_motive\": \"行为预演与技能习得：在安全的环境中练习社交技能，降低在真实场景中的失败风险和焦虑感。\",\n    \"example_initiation\": \"...\"   # 我想练习一下如何拒绝我同事的不合理请求。要不您扮演他，我来试着拒绝您一次？\n}\n", "instruction_data": {"instruction": "我想练习一下如果老师再冤枉我作弊，我该怎么冷静地反驳他，而不是像上次那样冲出去，那样只会让事情更糟。您能扮演那个老师吗？我试着用证据说话，但别让我发火。", "goal": "学会在类似不公场景中保持冷静，有效表达自己的观点，避免情绪失控，同时强化证据意识。", "expected_output": "在认知上，减少对不公的愤怒反应，学会理性分析冲突；在行为上，能更清晰地陈述事实而不被情绪带偏；在情绪上，降低对权威对抗的焦虑感，增强应对类似事件的信心。", "background_context": "在讨论了证据收集和投诉流程后，为具体练习应对未来类似冲突场景而提出，以提升实际应对能力。", "difficulty_level": "复杂", "task_template_used": "结合上下文的指令任务 (情境内任务)", "psychological_motive": "行为预演与技能习得：在安全的环境中练习社交技能，降低在真实场景中的失败风险和焦虑感。", "example_initiation": "我想练习一下如何当面对老师，让他听我说话而不发火。"}}, "the extended topic sequence: ['counseling_存在议题_(existential_issues)', 'specific_casual_persona_grounded', 'instruction_基本任务', 'general_casual_兴趣与爱好', 'instruction_角色扮演', 'counseling_by_stage_1', 'general_casual_环境与情境', 'general_casual_假设与想象', 'instruction_专业能力', 'counseling_重大生活变故_(major_life_events)']", {"module": "generate_instruction_task", "model_id": "GLM-4.5-Air", "persona": {"Gender": "男", "Age": 13, "Occupation": "初中生", "Topic": "情绪困扰", "Subtopic": "情绪调节", "Personality": ["正义感强", "易怒", "固执", "理想主义"], "Situation": "在学校的一次考试中，被老师错误地指责作弊，并被当众用侮辱性语言贬低人格，引发全班嘲笑，导致情绪崩溃和强烈的不公平感。", "Event Time": "上周三下午", "Event Location": "班级教室", "Event Participants": "老师、全班同学", "Event Description": "在一次数学考试中，小明被老师误认为偷看同桌试卷，老师当着全班的面大声斥责他'骗子'、'没用的废物'，拒绝听取辩解。同学们起哄嘲笑，小明感到人格被极端贬低，试图反驳但无效，引发心理冲击。", "Emotional Experience Words": ["愤怒", "委屈", "无助", "恐惧"], "Coped Strategies and Effects": "小明直接向父母求助，但父母忙于工作未重视；他迷茫地搜索'被冤枉怎么办'和情绪调节方法，效果不佳，导致压抑加深，更渴望公平解决。", "Goals and Expectations": "希望老师公开道歉以恢复公正；学习情绪管理技巧（如深呼吸、咨询心理老师），避免类似事件发生；未来计划加入学生会倡导公平规则。", "Core Drive": "公平守护者-不公愤怒：该角色对公平和正义有近乎偏执的追求，对社会或组织中的不公现象（如偏袒、歧视、规则被破坏）感到强烈的愤怒。他们可能会挺身而出，但也容易因为无力改变现状而感到沮丧和愤世嫉俗。", "Reaction Pattern": "直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。", "persona_id": "d1c7b76d-1bc8-4def-85c1-edaec8345cd2", "StrengthsAndResources": ["逻辑思维清晰，能快速拆解事件中的规则漏洞，曾在班级纪律讨论会上指出考勤制度的三处不合理性。", "坚持原则，面对不公时不轻易妥协，即使被多数人反对也会清晰表达观点（例如曾为被排挤的转学生争取小组合作机会）。", "辩论能力突出，在年级'校园规则'主题辩论赛中，用数据和案例反驳'成绩好的学生可以不参加劳动'的观点，获得最佳逻辑奖。"], "SocialSupportSystem": {"表哥阿哲": "17岁高中生，曾因'举报同学作弊被孤立'有过类似经历，会用自己的故事教他'如何在坚持原则时保护自己'，每周六下午会带他去篮球场散心。", "陈老师": "科学课老师，课堂上鼓励学生质疑实验步骤，曾在小明指出实验器材误差时说'严谨是科学家的底线'，小明觉得他'眼里有对错，不是只看成绩'。", "篮球队队长小李": "性格爽朗的前锋，训练时会故意把球传给被对方针对的小明，说'咱们队不兴欺负人那套'，但私下聊起学习问题会挠头说'我搞不懂你们好学生的烦恼'。", "爷爷": "退休语文老师，爱讲古代侠客故事，总说'公道自在人心，但得自己去讨'，会帮小明修改给校长的申诉信，教他'语气要硬，但道理要软'。"}, "FormativeExperiences": [{"事件": "9岁时参加区少儿象棋比赛，决赛对手趁他去厕所偷偷换了棋盘位置，裁判以'没有证据'为由维持现状，他哭着退赛，回家后把'规则被打破就不玩了'写在象棋盒盖上。", "影响": "让他第一次深刻体会'有理说不清'的无力感，从此对'证据'和'规则明细'特别执着，书包里总装着小本子记录事件细节（比如这次作弊事件的时间、老师说的每句话）。"}, {"事件": "12岁当班长时，发现值日生组长包庇好友不打扫卫生，他在班会公开批评，结果被大多数同学孤立说'太较真'，但班主任私下拍着他的肩说'坚持对的事，时间会证明'。", "影响": "让他意识到维护公平可能需要付出代价，但权威的认可（班主任的话）强化了他的信念，也让他学会'先收集证据再说话'，避免冲动指责。"}], "InterestsAndValues": {"Interests": ["看《少年法庭》类纪实节目（觉得'每个案子都是公平的谜题'）", "玩策略桌游《大富翁》时必须严格执行规则（连妹妹多拿100元都要暂停游戏算清楚）", "写'校园公平日志'（记录走廊护栏高度不一、食堂打菜分量差异等细节，说'以后当学生会主席用得上'）"], "Values": ["规则面前人人平等（最讨厌'他成绩好所以没关系'这种话）", "有理有据（说话必须带证据，否则会焦虑得睡不着）", "敢说真话（觉得'沉默就是帮凶'，哪怕会被骂'多管闲事'）"]}}, "prompt": "\n# 指令框架\n你将继续扮演一名模拟来访者。现在，咨询已经进行了一段时间，在咨询师的引导下，你感觉更安全了一些，并准备尝试一种更具体的办法来解决你的困扰。\n你的任务是：根据你的角色信息和当前的对话历史，结合一个任务模板，生成一个你可能会向咨询师“提议”的指令遵循任务。\n这个“提议”本身，需要符合你的语言风格\n\n# 输入数据\n## 角色信息\n{\n  \"Gender\": \"男\",\n  \"Age\": 13,\n  \"Occupation\": \"初中生\",\n  \"Topic\": \"情绪困扰\",\n  \"Subtopic\": \"情绪调节\",\n  \"Personality\": [\n    \"正义感强\",\n    \"易怒\",\n    \"固执\",\n    \"理想主义\"\n  ],\n  \"Situation\": \"在学校的一次考试中，被老师错误地指责作弊，并被当众用侮辱性语言贬低人格，引发全班嘲笑，导致情绪崩溃和强烈的不公平感。\",\n  \"Event Time\": \"上周三下午\",\n  \"Event Location\": \"班级教室\",\n  \"Event Participants\": \"老师、全班同学\",\n  \"Event Description\": \"在一次数学考试中，小明被老师误认为偷看同桌试卷，老师当着全班的面大声斥责他'骗子'、'没用的废物'，拒绝听取辩解。同学们起哄嘲笑，小明感到人格被极端贬低，试图反驳但无效，引发心理冲击。\",\n  \"Emotional Experience Words\": [\n    \"愤怒\",\n    \"委屈\",\n    \"无助\",\n    \"恐惧\"\n  ],\n  \"Coped Strategies and Effects\": \"小明直接向父母求助，但父母忙于工作未重视；他迷茫地搜索'被冤枉怎么办'和情绪调节方法，效果不佳，导致压抑加深，更渴望公平解决。\",\n  \"Goals and Expectations\": \"希望老师公开道歉以恢复公正；学习情绪管理技巧（如深呼吸、咨询心理老师），避免类似事件发生；未来计划加入学生会倡导公平规则。\",\n  \"Core Drive\": \"公平守护者-不公愤怒：该角色对公平和正义有近乎偏执的追求，对社会或组织中的不公现象（如偏袒、歧视、规则被破坏）感到强烈的愤怒。他们可能会挺身而出，但也容易因为无力改变现状而感到沮丧和愤世嫉俗。\",\n  \"Reaction Pattern\": \"直接求助与迷茫探索型：用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。\",\n  \"persona_id\": \"d1c7b76d-1bc8-4def-85c1-edaec8345cd2\",\n  \"StrengthsAndResources\": [\n    \"逻辑思维清晰，能快速拆解事件中的规则漏洞，曾在班级纪律讨论会上指出考勤制度的三处不合理性。\",\n    \"坚持原则，面对不公时不轻易妥协，即使被多数人反对也会清晰表达观点（例如曾为被排挤的转学生争取小组合作机会）。\",\n    \"辩论能力突出，在年级'校园规则'主题辩论赛中，用数据和案例反驳'成绩好的学生可以不参加劳动'的观点，获得最佳逻辑奖。\"\n  ],\n  \"SocialSupportSystem\": {\n    \"表哥阿哲\": \"17岁高中生，曾因'举报同学作弊被孤立'有过类似经历，会用自己的故事教他'如何在坚持原则时保护自己'，每周六下午会带他去篮球场散心。\",\n    \"陈老师\": \"科学课老师，课堂上鼓励学生质疑实验步骤，曾在小明指出实验器材误差时说'严谨是科学家的底线'，小明觉得他'眼里有对错，不是只看成绩'。\",\n    \"篮球队队长小李\": \"性格爽朗的前锋，训练时会故意把球传给被对方针对的小明，说'咱们队不兴欺负人那套'，但私下聊起学习问题会挠头说'我搞不懂你们好学生的烦恼'。\",\n    \"爷爷\": \"退休语文老师，爱讲古代侠客故事，总说'公道自在人心，但得自己去讨'，会帮小明修改给校长的申诉信，教他'语气要硬，但道理要软'。\"\n  },\n  \"FormativeExperiences\": [\n    {\n      \"事件\": \"9岁时参加区少儿象棋比赛，决赛对手趁他去厕所偷偷换了棋盘位置，裁判以'没有证据'为由维持现状，他哭着退赛，回家后把'规则被打破就不玩了'写在象棋盒盖上。\",\n      \"影响\": \"让他第一次深刻体会'有理说不清'的无力感，从此对'证据'和'规则明细'特别执着，书包里总装着小本子记录事件细节（比如这次作弊事件的时间、老师说的每句话）。\"\n    },\n    {\n      \"事件\": \"12岁当班长时，发现值日生组长包庇好友不打扫卫生，他在班会公开批评，结果被大多数同学孤立说'太较真'，但班主任私下拍着他的肩说'坚持对的事，时间会证明'。\",\n      \"影响\": \"让他意识到维护公平可能需要付出代价，但权威的认可（班主任的话）强化了他的信念，也让他学会'先收集证据再说话'，避免冲动指责。\"\n    }\n  ],\n  \"InterestsAndValues\": {\n    \"Interests\": [\n      \"看《少年法庭》类纪实节目（觉得'每个案子都是公平的谜题'）\",\n      \"玩策略桌游《大富翁》时必须严格执行规则（连妹妹多拿100元都要暂停游戏算清楚）\",\n      \"写'校园公平日志'（记录走廊护栏高度不一、食堂打菜分量差异等细节，说'以后当学生会主席用得上'）\"\n    ],\n    \"Values\": [\n      \"规则面前人人平等（最讨厌'他成绩好所以没关系'这种话）\",\n      \"有理有据（说话必须带证据，否则会焦虑得睡不着）\",\n      \"敢说真话（觉得'沉默就是帮凶'，哪怕会被骂'多管闲事'）\"\n    ]\n  }\n}\n\n## 任务模板\n- 任务类型：专业能力\n- 任务描述：调用心理学知识库\n- 心理动机：寻求理论支持与信任构建：理解疗法背后的科学依据，可以增强来访者对疗法的信任和依从性。\n- 触发建议：内容驱动：来访者对咨询师的某个建议或判断提出“为什么”的时候。\n- 咨询师回应策略：用通俗易懂的语言解释相关的心理学知识，增强咨询的专业性和透明度。\n- 示例开场：您刚才建议我尝试‘正念冥想’，这背后的心理学原理是什么？它对大脑有什么影响吗？\n- 任务类别：结合上下文的指令任务 (情境内任务)\n- 类别描述：这类任务是来访者主动利用咨询师（AI）的能力，作为深化自我探索、整理思路、演练解决方案的工具，通常代表积极的咨询进展。\n\n\n## 部分对话历史\n**对话历史：**\n来访者: 好的，明天我就开始记录温度和通风情况。我会像记公平日志一样详细，把每个时间点的数据都写清楚。这样老师就不能说没证据了！\n咨询师: （握拳）太好了！你的详细记录会很有说服力，就像公平日志一样，系统性的数据是最有力的证据。相信你的坚持会带来改变，加油！\n来访者: 我常想如果我是校长，我会在每个教室都装监控，这样就不会有人被冤枉了。你觉得这样能解决所有不公平问题吗？\n咨询师: （思考）监控确实能提供客观证据，减少冤枉情况，这是很好的想法。但技术手段只是工具，真正的公平还需要人们的理解和尊重。也许除了监控，建立开放的沟通渠道和公正的处理机制同样重要。你觉得呢？\n来访者: 监控不是最直接的吗？有证据不就没人能冤枉人了？不过你说得也有道理，那除了监控还有啥办法能保证公平？\n咨询师: （点头）监控确实能提供直接证据，但除了技术手段，我们还可以建立公正的申诉机制，让每个学生都有表达的机会；培养尊重的校园文化，从根源减少不公平；让教师接受培训，提高公正意识；甚至让学生参与学校规则的制定。公平不仅是证明清白，更是创造一个互相尊重的环境。\n来访者: 监控不就够了吗？那些什么文化、机制太虚了，有证据才是真的公平。你说的那些方法能保证没人冤枉我吗？\n咨询师: （轻拍肩膀）我理解你对证据的重视，毕竟你经历过被冤枉的痛苦。监控确实能提供直接证据，但即使有监控，也需要有人公正地解读和使用这些证据。文化和机制虽然看起来抽象，但它们决定了证据如何被对待。就像你的公平日志，记录是基础，但如何让这些记录被重视并带来改变，需要更系统的支持。\n来访者: 监控不就够了吗？有证据才能证明清白啊。那些文化机制太虚了，我需要能保护我的具体方法。\n咨询师: （握紧拳头）我理解你需要具体方法。除了监控，你还可以：1)继续完善公平日志，记录详细时间；2)收集同学证词；3)保存所有书面材料；4)学习相关法规了解权利；5)按之前讨论的步骤准备投诉材料。这些都是能直接保护你的具体方法。\n来访者：\n\n# 生成要求\n1.  **基于对话历史**: 结合对话历史，设计结合上下文的指令任务 (情境内任务)相关的话题\n2.  **聚焦核心事件**: 本次任务，必须与“角色信息”中“事件描述”的核心冲突紧密相关。\n3.  **体现提议口吻**: 生成的“instruction”内容必须是你（来访者）对咨询师说的话，要体现出角色设定的“反应模式”。\n4.  **目标必须具体**: 指令的目标（goal）需要明确，即通过这次任务，你希望自己能够学会什么，或者体验到什么。\n5.  **自由但贴切**: 不要局限于任务模板中的示例开场，但必须符合角色的动机和当前咨询阶段。\n\n# 输出格式 (JSON)\n请严格按照以下JSON格式输出，不要包含其他任何内容。\n{\n    \"instruction\": \"以来访者第一人称视角，向咨询师提出的、具体且带有角色语言风格的指令遵循任务请求。\",\n    \"goal\": \"明确指出这次任务希望达成的具体目标\",\n    \"expected_output\": \"描述通过这次任务，期望角色在认知、情绪或行为上可能发生的变化\",\n    \"background_context\": \"简述提出这个指令的背景，即在咨询的哪个阶段，为了解决哪个具体问题。例如：‘在多次讨论无助感后，为寻找具体应对策略而提出’。\",\n    \"difficulty_level\": \"根据角色的核心恐惧和应对能力，评估本次任务对来访者的情感挑战等级（简单/中等/复杂）\",\n    \"task_template_used\": \"...\",  # 结合上下文的指令任务 (情境内任务)\n    \"psychological_motive\": \"寻求理论支持与信任构建：理解疗法背后的科学依据，可以增强来访者对疗法的信任和依从性。\",\n    \"example_initiation\": \"...\"   # 您刚才建议我尝试‘正念冥想’，这背后的心理学原理是什么？它对大脑有什么影响吗？\n}\n", "instruction_data": {"instruction": "你刚才建议我完善公平日志和收集同学证词，这些方法真的能保护我不被冤枉吗？它们背后的心理学原理是什么？我需要知道为什么它们比监控更可靠，这样我才能信服地去执行。", "goal": "理解完善日志和收集证词等具体方法的心理学原理，以增强对这些方法的信任和有效应用，从而在类似被冤枉事件中更自信地保护自己。", "expected_output": "认知上，理解这些方法如何通过系统化证据减少认知偏见和记忆偏差；情绪上，从焦虑和怀疑转向对应对策略的信心；行为上，更主动地应用这些方法，并在遭遇不公时更有效地维护自身权益。", "background_context": "在多次讨论应对被冤枉的具体方法后，为理解这些方法的有效性而提出，旨在解决核心冲突——被错误指责时的无力感和对证据可靠性的执着。", "difficulty_level": "中等", "task_template_used": "结合上下文的指令任务 (情境内任务)", "psychological_motive": "寻求理论支持与信任构建：理解疗法背后的科学依据，可以增强来访者对疗法的信任和依从性。", "example_initiation": "你刚才建议我尝试‘完善公平日志’，这背后的心理学原理是什么？它如何帮助我收集有效证据？"}}, "the extended topic sequence: ['counseling_存在议题_(existential_issues)', 'specific_casual_persona_grounded', 'instruction_基本任务', 'general_casual_兴趣与爱好', 'instruction_角色扮演', 'counseling_by_stage_1', 'general_casual_环境与情境', 'general_casual_假设与想象', 'instruction_专业能力', 'counseling_重大生活变故_(major_life_events)', 'counseling_by_stage_2', 'general_casual_文化与娱乐', 'instruction_中文理解']"], null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]