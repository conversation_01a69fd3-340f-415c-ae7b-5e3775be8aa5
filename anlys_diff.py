#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import re
import argparse
from collections import Counter
from typing import Any, Dict, List, Tuple
import jieba
import jieba.posseg as pseg
# import matplotlib.pyplot as plt
# from wordcloud import WordCloud
# import matplotlib
# matplotlib.use('Agg')  # 非交互式后端，避免显示问题

# 设置默认字体路径
DEFAULT_FONT_PATH = '/home/<USER>/Client-simulation/Ubuntu_18.04_SimHei.ttf'

# 配置matplotlib使用中文字体
# if os.path.exists(DEFAULT_FONT_PATH):
#     matplotlib.font_manager.fontManager.addfont(DEFAULT_FONT_PATH)
#     plt.rcParams['font.family'] = 'SimHei'
#     plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

# 扩展的停用词列表，包含更多常见词汇
COMMON_STOPWORDS = {
    # 原有停用词
    '觉得', '比如', '它们', '这次', '事件', '的', '了', '和', '是', '在', '我', '有', '与', '这', '他', '你', '但',
    '，', '。', '；', '：', '"', '"', '？', '！', '、', '对', '中', '从', '到', '为', '被', '用', '能', '可',
    '上', '下', '1', '2', '3', '4', '5', '以', '及', '等', '把', '就', '也', '或', '而', '很', '会', '那',
    '都', '说', '着', '将', '让', '做', '去', '还', '得', '时', '年', '月', '日',
    # 新增停用词
    '一个', '一些', '一种', '一直', '一般', '一样', '不是', '不会', '不能', '不要', '不过', '不同', '没有',
    '可以', '应该', '需要', '希望', '想要', '觉得', '认为', '表示', '显示', '出现', '发生', '进行', '开始',
    '结束', '完成', '继续', '保持', '成为', '变成', '形成', '产生', '造成', '导致', '引起', '带来', '提供',
    '获得', '得到', '拥有', '具有', '包含', '涉及', '关于', '由于', '因为', '所以', '然后', '接着', '最后',
    '首先', '其次', '另外', '此外', '同时', '然而', '但是', '虽然', '尽管', '如果', '假如', '当', '在于',
    '通过', '根据', '按照', '依据', '基于', '来自', '来源', '方面', '方式', '方法', '途径', '手段', '措施',
    '行为', '活动', '过程', '阶段', '步骤', '环节', '部分', '内容', '信息', '数据', '资料', '材料', '文件',
    '记录', '报告', '结果', '效果', '作用', '影响', '意义', '价值', '重要', '主要', '基本', '核心', '关键',
    '特别', '特殊', '具体', '详细', '明确', '清楚', '明显', '显然', '当然', '确实', '实际', '真正', '完全',
    '非常', '十分', '相当', '比较', '更加', '最为', '极其', '特别', '尤其', '特别是', '尤其是', '妈妈', '发现', '父母', '参加', '父亲', '经历', '班级', '同学', '分享', '同班', '知道', '能力', '擅长', '朋友', '复杂', '时会', '母亲', '情绪', '变化', '认知', '突然', '体会', '喜欢', '讨厌', '真诚', '', ''
}

# 领域专用词典 - 用于改进分词效果
DOMAIN_WORDS = {
    # 心理学相关
    '心理咨询师', '心理医生', '心理治疗', '心理健康', '心理状态', '心理压力', '心理创伤', '心理支持',
    '情绪管理', '情绪调节', '情绪表达', '情绪波动', '情感依赖', '情感支持', '情感表达', '情感需求',
    '焦虑症', '抑郁症', '强迫症', '恐惧症', '社交恐惧', '分离焦虑', '创伤后应激', '适应障碍',
    '认知偏差', '认知重构', '行为模式', '应对策略', '防御机制', '自我认知', '自我价值', '自我效能',

    # 人际关系相关
    '人际关系', '人际交往', '人际沟通', '人际冲突', '人际边界', '社交技能', '社交圈子', '社交媒体',
    '亲密关系', '恋爱关系', '婚姻关系', '家庭关系', '亲子关系', '同伴关系', '师生关系', '同事关系',
    '信任危机', '沟通障碍', '情感冷漠', '情感疏离', '依恋模式', '安全感', '归属感', '孤独感',

    # 性格特质相关
    '内向型', '外向型', '完美主义', '强迫性', '依赖性', '回避型', '焦虑型', '边缘型', '自恋型',
    '敏感性', '情绪化', '理性化', '冲动性', '控制欲', '占有欲', '表现欲', '求知欲', '成就欲',

    # 生活场景相关
    '学习压力', '工作压力', '经济压力', '家庭压力', '同伴压力', '社会压力', '升学压力', '就业压力',
    '生活节奏', '生活方式', '生活质量', '生活状态', '生活环境', '生活习惯', '作息规律', '饮食习惯'
}

def initialize_jieba_dict():
    """
    初始化jieba分词器的自定义词典
    """
    # 添加领域专用词汇到jieba词典
    for word in DOMAIN_WORDS:
        jieba.add_word(word)

    # 尝试启用paddle模式提高准确性（如果可用）
    try:
        jieba.enable_paddle()
        print("✅ 已启用jieba paddle模式")
    except Exception as e:
        print(f"⚠️  无法启用jieba paddle模式，使用默认模式: {e}")
        pass

def extract_text_from_keys_and_values(data: Any, include_keys: bool = True) -> str:
    """
    从JSON数据中提取所有文本内容，包括键名和值

    Args:
        data: JSON数据（可能是字典、列表或其他类型）
        include_keys: 是否包含字典的键名

    Returns:
        str: 提取的所有文本内容
    """
    texts = []

    if isinstance(data, dict):
        for key, value in data.items():
            # 包含键名（如果需要）
            if include_keys and isinstance(key, str):
                texts.append(key)
            # 递归处理值
            texts.append(extract_text_from_keys_and_values(value, include_keys))
    elif isinstance(data, list):
        for item in data:
            texts.append(extract_text_from_keys_and_values(item, include_keys))
    elif isinstance(data, str):
        texts.append(data)
    elif data is not None:
        # 其他类型转为字符串
        texts.append(str(data))

    return " ".join(filter(None, texts))

def normalize_field_value(value: Any, include_keys: bool = False) -> str:
    """
    将各种类型的字段值标准化为字符串，用于文本分析
    改进版本：更好地处理嵌套结构和不同数据类型

    Args:
        value: 任意类型的字段值（字符串、列表、字典等）
        include_keys: 是否包含字典的键名

    Returns:
        str: 标准化后的字符串
    """
    return extract_text_from_keys_and_values(value, include_keys)

def advanced_word_segmentation(text: str, use_pos_tagging: bool = True) -> List[str]:
    """
    改进的分词函数，使用多种策略提高分词准确性

    Args:
        text: 待分词的文本
        use_pos_tagging: 是否使用词性标注过滤

    Returns:
        List[str]: 分词结果列表
    """
    if not text or not text.strip():
        return []

    # 预处理：清理文本
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)  # 保留中文、英文、数字
    text = re.sub(r'\s+', ' ', text).strip()  # 规范化空格

    words = []

    if use_pos_tagging:
        # 使用词性标注，只保留有意义的词性
        meaningful_pos = {'n', 'nr', 'ns', 'nt', 'nw', 'nz',  # 名词类
                         'v', 'vd', 'vn', 'vshi', 'vyou', 'vf', 'vx',  # 动词类
                         'a', 'ad', 'an', 'ag', 'al',  # 形容词类
                         'i', 'j', 'l', 'b', 'z'}  # 其他有意义词性

        for word, pos in pseg.cut(text):
            if (len(word.strip()) > 1 and
                word.strip() not in COMMON_STOPWORDS and
                pos in meaningful_pos):
                words.append(word.strip())
    else:
        # 普通分词
        for word in jieba.cut(text, cut_all=False):
            if (len(word.strip()) > 1 and
                word.strip() not in COMMON_STOPWORDS):
                words.append(word.strip())

    # 额外的后处理：合并可能被错误分割的词汇
    words = merge_split_words(words)

    return words

def merge_split_words(words: List[str]) -> List[str]:
    """
    合并可能被错误分割的词汇

    Args:
        words: 分词结果列表

    Returns:
        List[str]: 合并后的词汇列表
    """
    if not words:
        return words

    merged_words = []
    i = 0

    while i < len(words):
        current_word = words[i]

        # 检查是否可以与下一个词合并
        if i + 1 < len(words):
            next_word = words[i + 1]
            combined = current_word + next_word

            # 如果合并后的词在领域词典中，则合并
            if combined in DOMAIN_WORDS:
                merged_words.append(combined)
                i += 2
                continue

            # 检查三个词的组合
            if i + 2 < len(words):
                third_word = words[i + 2]
                combined_three = current_word + next_word + third_word
                if combined_three in DOMAIN_WORDS:
                    merged_words.append(combined_three)
                    i += 3
                    continue

        merged_words.append(current_word)
        i += 1

    return merged_words

def load_json_data(file_path: str) -> List[Dict]:
    """
    加载JSON文件数据，支持多种格式

    Args:
        file_path: JSON文件路径

    Returns:
        List[Dict]: JSON数据列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()

        # 尝试解析JSON
        try:
            data = json.loads(content)
        except json.JSONDecodeError as e:
            # 如果解析失败，尝试逐行解析（JSONL格式）
            print(f"尝试按JSONL格式解析文件...")
            data = []
            for line_num, line in enumerate(content.split('\n'), 1):
                line = line.strip()
                if line:
                    try:
                        data.append(json.loads(line))
                    except json.JSONDecodeError:
                        print(f"警告：第{line_num}行JSON格式错误，跳过")
                        continue

        if isinstance(data, dict):
            return [data]  # 单个对象转为列表
        elif isinstance(data, list):
            return data
        else:
            print(f"警告：JSON数据格式不是字典或列表，类型为: {type(data)}")
            return []

    except Exception as e:
        print(f"读取JSON文件失败: {e}")
        return []

def analyze_word_frequency(text: str, top_n: int = 30, additional_stopwords: set = None,
                          use_advanced_segmentation: bool = True) -> List[Tuple[str, int]]:
    """
    改进的文本词频分析函数

    Args:
        text: 要分析的文本
        top_n: 返回前N个高频词
        additional_stopwords: 额外的停用词集合
        use_advanced_segmentation: 是否使用改进的分词方法

    Returns:
        List[Tuple[str, int]]: 词频统计结果，格式为 [(词1, 频次1), (词2, 频次2), ...]
    """
    if not text or not text.strip():
        return []

    # 使用改进的分词方法
    if use_advanced_segmentation:
        words = advanced_word_segmentation(text, use_pos_tagging=True)
    else:
        words = list(jieba.cut(text))

    # 合并停用词
    stopwords = COMMON_STOPWORDS.copy()
    if additional_stopwords:
        stopwords.update(additional_stopwords)

    # 过滤停用词和无意义词汇
    filtered_words = []
    for word in words:
        word = word.strip()
        if (word and
            word not in stopwords and
            len(word) > 1 and
            not word.isdigit() and  # 过滤纯数字
            not re.match(r'^[a-zA-Z]+$', word)):  # 过滤纯英文（可选）
            filtered_words.append(word)

    # 统计词频
    word_counts = Counter(filtered_words)

    # 返回前N个高频词
    return word_counts.most_common(top_n)

def analyze_field_specific_words(data: List[Dict], field_name: str,
                                include_keys: bool = True) -> Dict[str, Any]:
    """
    针对特定字段进行深度词频分析

    Args:
        data: JSON数据列表
        field_name: 要分析的字段名
        include_keys: 是否包含字典键名在分析中

    Returns:
        Dict: 详细的分析结果
    """
    field_data = []
    field_types = Counter()

    # 提取字段数据并统计类型
    for item in data:
        if field_name in item:
            value = item[field_name]
            field_data.append(value)
            field_types[type(value).__name__] += 1

    if not field_data:
        return {
            'field_name': field_name,
            'total_records': 0,
            'field_types': {},
            'word_frequency': [],
            'unique_words': 0,
            'total_words': 0
        }

    # 提取所有文本内容
    all_text = ""
    for value in field_data:
        text = normalize_field_value(value, include_keys=include_keys)
        all_text += " " + text

    # 词频分析
    word_freq = analyze_word_frequency(all_text, top_n=50, use_advanced_segmentation=True)

    # 统计信息
    all_words = advanced_word_segmentation(all_text, use_pos_tagging=True)
    unique_words = len(set(all_words))
    total_words = len(all_words)

    return {
        'field_name': field_name,
        'total_records': len(field_data),
        'field_types': dict(field_types),
        'word_frequency': word_freq,
        'unique_words': unique_words,
        'total_words': total_words,
        'text_sample': all_text[:200] + "..." if len(all_text) > 200 else all_text
    }

# def generate_wordcloud(text: str, output_path: str, title: str = "词云图",
#                       additional_stopwords: set = None, font_path: str = DEFAULT_FONT_PATH,
#                       use_advanced_segmentation: bool = True):
#     """
#     改进的词云生成函数
#
#     Args:
#         text: 文本内容
#         output_path: 输出文件路径
#         title: 词云图标题
#         additional_stopwords: 额外的停用词集合
#         font_path: 字体文件路径
#         use_advanced_segmentation: 是否使用改进的分词方法
#     """
#     if not text or not text.strip():
#         print(f"警告：文本内容为空，无法生成词云图")
#         return
#
#     # 使用改进的分词方法
#     if use_advanced_segmentation:
#         words = advanced_word_segmentation(text, use_pos_tagging=True)
#     else:
#         words = list(jieba.cut(text))
#
#     # 合并停用词
#     stopwords = COMMON_STOPWORDS.copy()
#     if additional_stopwords:
#         stopwords.update(additional_stopwords)
#
#     # 过滤停用词并构建词频字典
#     word_freq = Counter()
#     for word in words:
#         word = word.strip()
#         if (word and
#             word not in stopwords and
#             len(word) > 1 and
#             not word.isdigit()):
#             word_freq[word] += 1
#
#     if not word_freq:
#         print(f"警告：过滤后无有效词汇，无法生成词云图")
#         return
#
#     # 确保输出目录存在
#     os.makedirs(os.path.dirname(output_path), exist_ok=True)
#
#     # 配置词云
#     wordcloud_config = {
#         'width': 1200,
#         'height': 600,
#         'background_color': 'white',
#         'max_words': 150,
#         'max_font_size': 120,
#         'min_font_size': 10,
#         'random_state': 42,
#         'collocations': False,  # 避免重复词汇组合
#         'relative_scaling': 0.5,
#         'colormap': 'viridis'
#     }
#
#     if os.path.exists(font_path):
#         wordcloud_config['font_path'] = font_path
#     else:
#         print(f"警告：找不到字体文件 {font_path}，可能导致中文显示异常")
#
#     # 生成词云
#     try:
#         wordcloud = WordCloud(**wordcloud_config).generate_from_frequencies(word_freq)
#
#         # 绘制词云图
#         plt.figure(figsize=(15, 8))
#         plt.imshow(wordcloud, interpolation='bilinear')
#         plt.axis('off')
#         plt.title(title, fontsize=16, pad=20)
#         plt.tight_layout()
#
#         # 保存图片
#         plt.savefig(output_path, dpi=300, bbox_inches='tight')
#         plt.close()
#
#         print(f"词云图已保存至: {output_path}")
#
#     except Exception as e:
#         print(f"生成词云图失败: {e}")

def comprehensive_json_analysis(data: List[Dict], output_dir: str = "analysis_output") -> Dict[str, Any]:
    """
    对JSON数据进行全面的词频分析

    Args:
        data: JSON数据列表
        output_dir: 输出目录

    Returns:
        Dict: 全面的分析结果
    """
    if not data:
        return {'error': '没有数据可分析'}

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 初始化jieba词典
    initialize_jieba_dict()

    results = {
        'total_records': len(data),
        'field_analysis': {},
        'overall_analysis': {},
        # 'word_clouds': []
    }

    # 收集所有字段名
    all_fields = set()
    for item in data:
        if isinstance(item, dict):
            all_fields.update(item.keys())

    print(f"发现 {len(all_fields)} 个不同的字段: {', '.join(sorted(all_fields))}")

    # 分析每个字段
    for field in sorted(all_fields):
        print(f"\n分析字段: {field}")

        # 分析字段（包含键名）
        field_analysis_with_keys = analyze_field_specific_words(data, field, include_keys=True)
        # 分析字段（不包含键名）
        field_analysis_without_keys = analyze_field_specific_words(data, field, include_keys=False)

        results['field_analysis'][field] = {
            'with_keys': field_analysis_with_keys,
            'without_keys': field_analysis_without_keys
        }

        # 生成该字段的词云图
        # if field_analysis_with_keys['total_words'] > 0:
        #     field_text = ""
        #     for item in data:
        #         if field in item:
        #             field_text += " " + normalize_field_value(item[field], include_keys=True)
        #
        #     wordcloud_path = os.path.join(output_dir, f"{field}_wordcloud.png")
        #     generate_wordcloud(field_text, wordcloud_path, f"{field} 字段词云图",
        #                      font_path=font_path, use_advanced_segmentation=True)
        #     results['word_clouds'].append({
        #         'field': field,
        #         'path': wordcloud_path
        #     })

    # 整体分析（所有字段合并）
    all_text_with_keys = ""
    all_text_without_keys = ""

    for item in data:
        all_text_with_keys += " " + extract_text_from_keys_and_values(item, include_keys=True)
        all_text_without_keys += " " + extract_text_from_keys_and_values(item, include_keys=False)

    # 整体词频分析
    overall_freq_with_keys = analyze_word_frequency(all_text_with_keys, top_n=100, use_advanced_segmentation=True)
    overall_freq_without_keys = analyze_word_frequency(all_text_without_keys, top_n=100, use_advanced_segmentation=True)

    results['overall_analysis'] = {
        'with_keys': {
            'word_frequency': overall_freq_with_keys,
            'total_words': len(advanced_word_segmentation(all_text_with_keys)),
            'unique_words': len(set(advanced_word_segmentation(all_text_with_keys)))
        },
        'without_keys': {
            'word_frequency': overall_freq_without_keys,
            'total_words': len(advanced_word_segmentation(all_text_without_keys)),
            'unique_words': len(set(advanced_word_segmentation(all_text_without_keys)))
        }
    }

    # 生成整体词云图
    # overall_wordcloud_path = os.path.join(output_dir, "overall_wordcloud.png")
    # generate_wordcloud(all_text_with_keys, overall_wordcloud_path, "整体词云图（包含字段名）",
    #                   font_path=font_path, use_advanced_segmentation=True)
    #
    # overall_wordcloud_no_keys_path = os.path.join(output_dir, "overall_wordcloud_no_keys.png")
    # generate_wordcloud(all_text_without_keys, overall_wordcloud_no_keys_path, "整体词云图（仅内容）",
    #                   font_path=font_path, use_advanced_segmentation=True)
    #
    # results['word_clouds'].extend([
    #     {'field': 'overall_with_keys', 'path': overall_wordcloud_path},
    #     {'field': 'overall_without_keys', 'path': overall_wordcloud_no_keys_path}
    # ])

    return results

# 旧的分析函数已被新的comprehensive_json_analysis函数替代
# 保留一些辅助函数供特殊需求使用

def extract_supporter_roles(text: str, top_n: int = 30) -> List[Tuple[str, int]]:
    """
    从文本中提取支持者角色并统计频次（保留用于特殊分析需求）

    Args:
        text: 包含支持者角色的文本
        top_n: 返回前N个高频角色

    Returns:
        List[Tuple[str, int]]: 角色频次统计结果
    """
    # 支持者角色相关词汇（已整合到DOMAIN_WORDS中）
    supporter_roles = {
        '亲属': ['父亲', '母亲', '爸爸', '妈妈', '哥哥', '姐姐', '弟弟', '妹妹', '爷爷', '奶奶', '外公', '外婆',
                '叔叔', '阿姨', '舅舅', '舅妈', '姑姑', '姑父', '表哥', '表姐', '表弟', '表妹', '堂哥', '堂姐',
                '堂弟', '堂妹', '儿子', '女儿', '孙子', '孙女'],
        '学校': ['老师', '同学', '班主任', '校长', '辅导员', '导师', '教授', '助教', '班长', '学生'],
        '职场': ['同事', '上司', '领导', '老板', '经理', '主管', '部门', '团队', '员工', '下属', '客户'],
        '朋友': ['朋友', '好友', '密友', '挚友', '知己', '闺蜜', '兄弟', '姐妹', '伙伴'],
        '社交': ['网友', '粉丝', '社区', '群组', '俱乐部', '邻居', '室友'],
        '专业人士': ['医生', '心理医生', '心理咨询师', '律师', '会计', '教练', '治疗师', '社工']
    }

    # 将支持者角色词汇扁平化为列表
    all_role_words = [role for category_roles in supporter_roles.values() for role in category_roles]

    # 使用改进的分词方法
    words = advanced_word_segmentation(text, use_pos_tagging=True)

    # 筛选支持者角色词
    role_words = [word for word in words if any(role in word for role in all_role_words)]

    # 统计词频
    role_counts = Counter(role_words)

    # 返回前N个高频角色
    return role_counts.most_common(top_n)

# 旧的特定字段分析函数已被comprehensive_json_analysis替代
# 如需特定字段分析，请使用新的analyze_field_specific_words函数

# 旧的特定字段分析函数已被删除，请使用comprehensive_json_analysis进行全面分析

def print_analysis_results(results: Dict[str, Any]):
    """
    打印分析结果的辅助函数

    Args:
        results: 分析结果字典
    """
    print(f"\n{'='*80}")
    print(f"JSON文件词频分析报告")
    print(f"{'='*80}")

    print(f"\n📊 基本信息:")
    print(f"  总记录数: {results['total_records']}")
    print(f"  发现字段数: {len(results['field_analysis'])}")

    # 整体分析结果
    if 'overall_analysis' in results:
        print(f"\n🌍 整体分析 (包含字段名):")
        overall_with_keys = results['overall_analysis']['with_keys']
        print(f"  总词数: {overall_with_keys['total_words']}")
        print(f"  唯一词数: {overall_with_keys['unique_words']}")
        print(f"  词汇丰富度: {overall_with_keys['unique_words']/overall_with_keys['total_words']:.3f}")

        print(f"\n  🔥 高频词汇 (前20个):")
        for i, (word, count) in enumerate(overall_with_keys['word_frequency'][:20], 1):
            print(f"    {i:2d}. {word}: {count}")

        print(f"\n🌍 整体分析 (仅内容，不含字段名):")
        overall_without_keys = results['overall_analysis']['without_keys']
        print(f"  总词数: {overall_without_keys['total_words']}")
        print(f"  唯一词数: {overall_without_keys['unique_words']}")
        print(f"  词汇丰富度: {overall_without_keys['unique_words']/overall_without_keys['total_words']:.3f}")

        print(f"\n  🔥 高频词汇 (前20个):")
        for i, (word, count) in enumerate(overall_without_keys['word_frequency'][:20], 1):
            print(f"    {i:2d}. {word}: {count}")

    # 各字段分析结果
    print(f"\n📋 各字段详细分析:")
    for field_name, field_data in results['field_analysis'].items():
        print(f"\n  🏷️  字段: {field_name}")

        with_keys = field_data['with_keys']
        without_keys = field_data['without_keys']

        print(f"    记录数: {with_keys['total_records']}")
        print(f"    数据类型: {', '.join(with_keys['field_types'].keys())}")

        if with_keys['total_words'] > 0:
            print(f"    总词数 (含字段名): {with_keys['total_words']}")
            print(f"    唯一词数 (含字段名): {with_keys['unique_words']}")
            print(f"    总词数 (仅内容): {without_keys['total_words']}")
            print(f"    唯一词数 (仅内容): {without_keys['unique_words']}")

            print(f"    🔥 高频词汇 (含字段名, 前10个):")
            for word, count in with_keys['word_frequency'][:10]:
                print(f"      • {word}: {count}")

            print(f"    🔥 高频词汇 (仅内容, 前10个):")
            for word, count in without_keys['word_frequency'][:10]:
                print(f"      • {word}: {count}")
        else:
            print(f"    ⚠️  该字段无有效文本内容")

    # 词云图信息
    # if 'word_clouds' in results and results['word_clouds']:
    #     print(f"\n🎨 生成的词云图:")
    #     for cloud_info in results['word_clouds']:
    #         print(f"  • {cloud_info['field']}: {cloud_info['path']}")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='改进版JSON文件词频分析工具')
    parser.add_argument('file_path', help='JSON文件路径')
    parser.add_argument('--mode', choices=['comprehensive', 'legacy'], default='comprehensive',
                        help='分析模式：comprehensive(全面分析) 或 legacy(兼容旧版)')
    parser.add_argument('--fields', nargs='+',
                        help='指定要分析的字段名（仅在legacy模式下有效）')
    parser.add_argument('--output-dir', default='analysis_output',
                        help='输出目录路径')
    parser.add_argument('--font', default=DEFAULT_FONT_PATH,
                        help='指定中文字体文件路径')
    parser.add_argument('--include-keys', action='store_true', default=True,
                        help='在分析中包含JSON字段名（默认启用）')
    parser.add_argument('--top-words', type=int, default=50,
                        help='显示的高频词数量（默认50）')
    args = parser.parse_args()

    # 验证字体文件存在
    font_path = args.font
    if not os.path.exists(font_path):
        print(f"⚠️  警告：找不到指定的字体文件 {font_path}，可能导致中文显示异常")
    else:
        print(f"✅ 使用字体文件: {font_path}")

    # 加载数据
    print(f"📂 正在加载JSON文件: {args.file_path}")
    data = load_json_data(args.file_path)
    if not data:
        print("❌ 无法加载数据，程序退出")
        return

    print(f"✅ 已成功加载 {len(data)} 条数据记录")

    if args.mode == 'comprehensive':
        # 全面分析模式
        print(f"🚀 开始全面分析模式...")
        results = comprehensive_json_analysis(data, args.output_dir)

        if 'error' in results:
            print(f"❌ 分析失败: {results['error']}")
            return

        # 打印结果
        print_analysis_results(results)

        # 保存分析结果到文件
        results_file = os.path.join(args.output_dir, 'analysis_results.json')
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                # 转换结果为可序列化的格式
                serializable_results = {
                    'total_records': results['total_records'],
                    'field_analysis': {},
                    'overall_analysis': results['overall_analysis'],
                    # 'word_clouds': results['word_clouds']
                }

                for field, data in results['field_analysis'].items():
                    serializable_results['field_analysis'][field] = {
                        'with_keys': {
                            'total_records': data['with_keys']['total_records'],
                            'field_types': data['with_keys']['field_types'],
                            'word_frequency': data['with_keys']['word_frequency'][:args.top_words],
                            'unique_words': data['with_keys']['unique_words'],
                            'total_words': data['with_keys']['total_words']
                        },
                        'without_keys': {
                            'total_records': data['without_keys']['total_records'],
                            'field_types': data['without_keys']['field_types'],
                            'word_frequency': data['without_keys']['word_frequency'][:args.top_words],
                            'unique_words': data['without_keys']['unique_words'],
                            'total_words': data['without_keys']['total_words']
                        }
                    }

                json.dump(serializable_results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 详细分析结果已保存至: {results_file}")
        except Exception as e:
            print(f"⚠️  保存分析结果失败: {e}")

    else:
        # 兼容旧版模式
        print(f"🔄 使用兼容模式...")
        if not args.fields:
            print("❌ 兼容模式需要指定 --fields 参数")
            return

        # 这里可以保留原有的分析逻辑
        print("⚠️  兼容模式功能待实现，请使用 comprehensive 模式")

    print(f"\n🎉 分析完成！输出目录: {args.output_dir}")

if __name__ == "__main__":
    main()